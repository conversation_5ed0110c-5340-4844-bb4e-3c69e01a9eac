# This tells lombok this directory is the root,
# no need to look somewhere else for java code.
config.stopBubbling = true
# This will add the @lombok.Generated annotation
# to all the code generated by Lombok,
# so it can be excluded from coverage by jacoco.
lombok.addLombokGeneratedAnnotation = true

lombok.copyableAnnotations+=org.springframework.beans.factory.annotation.Qualifier
lombok.copyableAnnotations+=org.springframework.beans.factory.annotation.Value
