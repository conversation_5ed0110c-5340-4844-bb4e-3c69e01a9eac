# These are some examples of commonly ignored file patterns.
# You should customize this list as applicable to your project.
# Learn more about .gitignore:
#     https://www.atlassian.com/git/tutorials/saving-changes/gitignore

# Node artifact files
node_modules/
dist/

# Compiled Java class files
*.class

# Compiled Python bytecode
*.py[cod]

# Log files
*.log

# Package files
*.jar
!config/checkstyle/jar/*.jar

bin/
daemon/
wrapper/
caches/
.tmp/
native/

# Maven
target/
dist/

# JetBrains IDE
.idea/
.run/

# Unit test reports
TEST*.xml

# Generated by MacOS
.DS_Store

# Generated by Windows
Thumbs.db

# Applications
*.app
*.exe
*.war

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv

# Gradle
.gradle/
gradle/

# Build files
build/

# Local application properties
src/main/resources/application-local.properties