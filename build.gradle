buildscript {
    ext {
        springBootVersion = '3.3.11'
        ttbVersion = '5.0.56-dev.00710'
        dockerVersion = '0.35.0'
        springCloudAzureDependenciesVersion = '5.14.0'
        sonarqubeVersion = '4.4.1.3373'
        springCloudVersion = '2023.0.5'

        set('nexusUsername', project.hasProperty('nexusUser') ? project.getProperty('nexusUser') : 'nexusUser')
        set('nexusPassword', project.hasProperty('nexusPassword') ? project.getProperty('nexusPassword') : 'nexusPassword')
    }
    repositories {
        maven {
            url 'https://nexus.tmbbank.local:8081/repository/oneapp'
            credentials {
                username = mavenUser
                password = mavenPassword
            }
        }
        maven {
            url "https://nexus.tmbbank.local:8081/repository/plugins.gradle/"
            credentials {
                username = mavenUser
                password = mavenPassword
            }
        }
    }

    dependencies {
        classpath "org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}"
        classpath "com.palantir.gradle.docker:gradle-docker:${dockerVersion}"
        classpath "org.sonarsource.scanner.gradle:sonarqube-gradle-plugin:${sonarqubeVersion}"
    }
}

apply plugin: 'java'
apply plugin: 'eclipse'
apply plugin: 'org.springframework.boot'
apply plugin: 'io.spring.dependency-management'
apply plugin: 'com.palantir.docker'
apply plugin: 'jacoco'
apply plugin: 'project-report'
apply plugin: "org.sonarqube"
apply plugin: "checkstyle"
apply plugin: "groovy"

group = 'com.ttb.top'
version = '1.0.0'
java {
    sourceCompatibility = JavaVersion.VERSION_17
}

jar {
    enabled = false
    archiveClassifier = ''
}

if (project.hasProperty('projVersion')) {
    project.version = project.projVersion
} else {
    project.version = '1.0.0'
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    maven {
        url 'https://nexus.tmbbank.local:8081/repository/oneapp'
        credentials {
            username = mavenUser
            password = mavenPassword
        }
    }
}

springBoot {
    buildInfo()
}

dependencies {
    //TTB Dependencies for Library
    implementation(platform("com.ttb.top.library:ttb-dependencies-helper:${ttbVersion}"))
    annotationProcessor(platform("com.ttb.top.library:ttb-dependencies-helper:${ttbVersion}"))

    // spring
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-aop'
    implementation 'org.springframework.boot:spring-boot-starter-data-mongodb'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-cache'
    implementation 'org.ehcache:ehcache'
    implementation 'org.apache.commons:commons-lang3'
    implementation 'com.google.code.gson:gson'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    implementation 'io.github.resilience4j:resilience4j-spring-boot3'
    implementation 'io.github.resilience4j:resilience4j-all'

    // spring feign
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'

    //mapstruct
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok-mapstruct-binding'

    implementation 'io.micrometer:micrometer-registry-prometheus'
    implementation 'org.apache.commons:commons-lang3'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'
    implementation 'com.jayway.jsonpath:json-path'

    //TTB lib
    implementation 'com.ttb.top.library:common-model'
    implementation 'com.ttb.top.library:lookup-helper'
    implementation 'com.ttb.top.library:httpheader-helper'
    implementation 'com.ttb.top.library:exception-model'
    implementation 'com.ttb.top.library:utility-helper'
    implementation 'com.ttb.top.library:request-log-helper'
    implementation 'com.ttb.top.library:mongo-helper'
    implementation 'com.ttb.top.library:redis-cache-helper'

    // decrypt-helper
    implementation 'com.ttb.top.library:decrypt-helper'
    implementation 'jakarta.persistence:jakarta.persistence-api'

    implementation 'org.springframework:spring-context-support'
    testImplementation 'org.instancio:instancio-junit'

    //mapstruct
    implementation 'org.mapstruct:mapstruct'
    annotationProcessor 'org.mapstruct:mapstruct-processor'
    annotationProcessor 'org.projectlombok:lombok-mapstruct-binding'

    // kafka dependencies
    implementation 'org.springframework.kafka:spring-kafka'
    implementation 'org.apache.kafka:kafka-streams'
    testImplementation 'org.springframework.kafka:spring-kafka-test'

    //testing
    testImplementation 'org.spockframework:spock-spring'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.spockframework:spock-core'

    //integration testing
    testImplementation 'io.rest-assured:rest-assured'
    testImplementation 'org.springframework.boot:spring-boot-testcontainers'
    testImplementation 'org.testcontainers:spock'
    testImplementation 'org.testcontainers:mongodb'
    testImplementation 'org.wiremock:wiremock-standalone'
    testImplementation 'com.github.codemonstur:embedded-redis'

    runtimeOnly 'io.micrometer:micrometer-registry-prometheus'
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
        mavenBom "com.azure.spring:spring-cloud-azure-dependencies:${springCloudAzureDependenciesVersion}"
    }
}


tasks.named('test') {
    useJUnitPlatform()
}

tasks.withType(Test) {
    testLogging {
        events "passed", "skipped", "failed"
    }
}

docker {
    name "com.ttb.top/${project.name}:${project.version}"
    dockerfile file('Dockerfile')
    files jar.archiveFile
    buildArgs(['JAR_FILE': "${jar.archiveFileName}"])
}

tasks.getByPath('dockerPrepare').dependsOn('bootJar')
tasks.getByPath('dockerPrepare').dependsOn('jar')
tasks.getByPath('docker').dependsOn('build')

jacoco {
    toolVersion = "0.8.11"
}

jacocoTestReport {
    reports {
        html.required = true
        xml.required = true
        csv.required = false
    }
}

sonar {
    if (System.getProperty("sonar.host.url").equals(null)) {
        properties {
            System.setProperty('sonar.host.url', 'http://localhost:9000')
            System.setProperty('sonar.projectKey', 'dnc-update-stream-service')
            System.setProperty('sonar.projectName', 'dnc-update-stream-service')
            System.setProperty('sonar.login', project.getProperty('sonarLogin'))
            System.setProperty('sonar.password', project.getProperty('sonarPassword'))
        }
    }
    properties {
        property 'sonar.coverage.exclusions', '**/configuration/**, **/config/**, **/model/**, **/dto/**, **/wsdl/**, **/entity/**, **/utils/**, **/constant/*, **/repository/*, **/exception/*, **/DncUpdateStreamServiceApplication.java'
    }
    properties {
        property 'sonar.exclusions', '**/configuration/**, **/config/**, **/model/**, **/dto/**, **/wsdl/**, **/entity/**, **/repository/**, **/utils/**'
    }
}

test {
    testLogging.showStandardStreams = true
    finalizedBy "jacocoTestReport", "installLocalGitHook"
}

checkstyle {
    toolVersion = '10.15.0'
    configFile = file("${rootDir}/config/checkstyle/checkstyle.xml")
    maxWarnings = 0
    maxErrors = 0
}

tasks.withType(Checkstyle) {
    reports {
        xml.required = false
        html.required = true
        sarif.required = true
    }
}
tasks.register("installLocalGitHook", Copy) {
    from new File(rootProject.rootDir, 'scripts/pre-commit')
    into { new File(rootProject.rootDir, '.git/hooks') }
    fileMode 0775
}
