## Build

`gradle build`

---

## Create a jar file

`gradle bootJar`

---

## Run with Local

1. Using intellij configuration `Run with Local`

---

## Run with Docker Compose

1. build jar file using `gradle bootJar`.

* `docker compose up` to start all services.
* `docker compose watch` to start all services and enable hot-reload mode for service.
* hot-reload triggered by running `gradle bootJar` then it will rebuild docker image.

---

## Unit Tests

Using Spock framework for unit testing. The Spock Framework is a powerful and flexible testing
framework that offers many advantages over other testing frameworks in the Java and Groovy
ecosystems. With its expressive and readable specification language, built-in support for
data-driven testing and mocking, seamless integration with other tools, and support for concurrent
and asynchronous testing, Spock makes it easy to write comprehensive and effective tests for your
Java and Groovy applications. -<PERSON>
[Spock Framework document](https://spockframework.org/spock/docs/1.3/all_in_one.html#_introduction)

* Create **groovy** class under test/groovy/unit package and add suffix "Test" to className.
  For example "GreetingControllerImpl**Test**"
* Run test via intellij or gradle command
    * Run all unit test `gradle test --tests "*Test"`
    * Run by specific test name `gradle test --tests "GreetingControllerImplTest"`

---

## Integration Tests

Using [Spock framework](https://spockframework.org/spock/docs/1.3/all_in_one.html#_introduction) + [Rest-assured](https://rest-assured.io/) + [Testcontainers](https://testcontainers.com/)

* Create **groovy** class under test/groovy/integration package and add suffix "IT" to className.
  For example "GreetingControllerImpl**IT**"
* If your test needs to connect to other services you need to add module container. Please see all
  modules and how to install [here.](https://java.testcontainers.org/)

* Run test via intellij or gradle command
    * Run all integration test `gradle test --tests "*IT"`
    * Run by specific test name `gradle test --tests "GreetingControllerImplIT"`

In case you want to run both unit test and integration test just run `gradle test`

---

## Test on Local environment
1. Start docker compose
    ```shell
    docker compose up -d
    ```
2. Start app `DnCUpdateStreamServiceApplication` with active profile `local`.
3. the http://localhost:8080/producer/insert can simulate publish message to event hub, than the consume process will 
trigger.

Note: There is also mock server for OneApp - Customer Care API and Common config API.
Its configurations can be modified to change their response.
(config files location `project_root/mock/config`)

---

## CheckStyles

---

## Troubleshoot

---

## AES encryption

To generate AES key. Run

```shell
./gen-aes-key.sh
```

Use generated key to update app property.

```
db.encryption.aes.key=<Generated key>
```
