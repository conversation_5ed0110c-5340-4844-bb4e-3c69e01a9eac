#!/bin/sh
SCRIPT_DIR=$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd)
CHECKSTYLE_PATH="${SCRIPT_DIR}/../../config/checkstyle"

java -Dconfig_loc="${CHECKSTYLE_PATH}" -jar $CHECKSTYLE_PATH/jar/checkstyle-10.15.0-all.jar -c $CHECKSTYLE_PATH/checkstyle.xml ./src/main | grep WARN > /dev/null;

if [ $? -eq 0 ]; then
    RED='\033[0;31m'
    echo "${RED}Code style violations detected. Aborting commit."
    exit 1;
fi

