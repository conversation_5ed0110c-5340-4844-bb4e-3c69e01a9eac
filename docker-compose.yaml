services:
#  service:
#    build:
#      context: .
#      dockerfile: Dockerfile
#    ports:
#      - "8080:8080"
#    depends_on:
#      - mongodb
#    develop:
#      watch:
#        - action: rebuild
#          path: ./build/libs
  mongodb:
    image: mongo:7.0
    command: [ "--replSet", "rs0", "--bind_ip_all", "--port", "27017" ]
    ports:
      - 27017:27017
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      - MONGO_INITDB_USERNAME=app_user
      - MONGO_INITDB_PASSWORD=app_password
      - MONGO_INITDB_DATABASE=topdevdb
    healthcheck:
      test: [ "CMD", "sh", "/data/init/init.sh" ]
      interval: 5s
      timeout: 30s
      start_period: 0s
      start_interval: 1s
      retries: 30
    volumes:
      - "./src/test/resources/docker/mongo/:/data/init/"
  redis:
    image: redis
    ports:
      - "6379:6379"
  kafka:
    image: confluentinc/cp-kafka:7.6.1
    ports:
      - "9092:9092"
    environment:
      - K<PERSON>KA_BROKER_ID=1
      - <PERSON><PERSON><PERSON>_ADVERTISED_LISTENERS=INTERNAL://kafka:29092,EXTERNAL://localhost:9092
      - KAFKA_LISTENER_SECURITY_PROTOCOL_MAP=CONTROLLER:PLAINTEXT,INTERNAL:PLAINTEXT,EXTERNAL:PLAINTEXT
      - KAFKA_INTER_BROKER_LISTENER_NAME=INTERNAL
      - KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR=1
      - KAFKA_CFG_AUTO_CREATE_TOPICS_ENABLE=true
      - KAFKA_PROCESS_ROLES=broker,controller
      - CLUSTER_ID=TjSaZTo9QQAdxl2qeyh81A==
      - KAFKA_CONTROLLER_QUORUM_VOTERS=1@kafka:29093
      - KAFKA_LISTENERS=INTERNAL://kafka:29092,CONTROLLER://kafka:29093,EXTERNAL://0.0.0.0:9092
      - KAFKA_CONTROLLER_LISTENER_NAMES=CONTROLLER
  sonarqube:
    image: sonarqube:latest
    environment:
      - SONAR_ES_BOOTSTRAP_CHECKS_DISABLE=true
    ports:
      - "9000:9000"
