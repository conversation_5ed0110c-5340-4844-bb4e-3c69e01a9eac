package com.ttb.top.dncupdatestreamsservice.configuration

import org.apache.kafka.clients.producer.ProducerConfig
import org.apache.kafka.common.serialization.StringSerializer
import org.springframework.context.annotation.Configuration
import org.springframework.kafka.core.DefaultKafkaProducerFactory
import org.springframework.kafka.core.KafkaTemplate
import org.springframework.test.util.ReflectionTestUtils
import spock.lang.Specification
import spock.lang.Subject

class EventHubProducerConfigTest extends Specification {

    @Subject
    EventHubProducerConfig producerConfig = new EventHubProducerConfig()

    def setup() {
        // Set default values using reflection
        ReflectionTestUtils.setField(producerConfig, "bootstrapServers", "localhost:9092")
        ReflectionTestUtils.setField(producerConfig, "saslJaasConfig", "test-jaas-config")
        ReflectionTestUtils.setField(producerConfig, "saslMechanism", "PLAIN")
        ReflectionTestUtils.setField(producerConfig, "securityProtocol", "SASL_SSL")
    }

    def "should have correct class annotations"() {
        expect: "class should be properly annotated"
        EventHubProducerConfig.class.isAnnotationPresent(Configuration)
    }

    def "should create ProducerFactory with correct configuration"() {
        when: "producerFactory is called"
        def factory = producerConfig.producerFactory()
        def configs = getProducerConfigs(factory)

        then: "should return DefaultKafkaProducerFactory with correct configuration"
        factory instanceof DefaultKafkaProducerFactory
        configs[ProducerConfig.BOOTSTRAP_SERVERS_CONFIG] == "localhost:9092"
        configs[ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG] == StringSerializer.class
        configs[ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG] == StringSerializer.class
        configs["sasl.mechanism"] == "PLAIN"
        configs["security.protocol"] == "SASL_SSL"
        configs["sasl.jaas.config"] == "test-jaas-config"
    }

    def "should create KafkaTemplate with correct producer factory"() {
        when: "kafkaTemplate is called"
        def template = producerConfig.kafkaTemplate()

        then: "should return KafkaTemplate with correct producer factory"
        template instanceof KafkaTemplate
        template.producerFactory instanceof DefaultKafkaProducerFactory
    }

    def "should verify value annotations on fields"() {
        when: "checking field annotations"
        def bootstrapServersField = EventHubProducerConfig.class.getDeclaredField("bootstrapServers")
        def saslJaasConfigField = EventHubProducerConfig.class.getDeclaredField("saslJaasConfig")
        def saslMechanismField = EventHubProducerConfig.class.getDeclaredField("saslMechanism")
        def securityProtocolField = EventHubProducerConfig.class.getDeclaredField("securityProtocol")

        then: "fields should have @Value annotations with correct property paths"
        bootstrapServersField.isAnnotationPresent(org.springframework.beans.factory.annotation.Value)
        bootstrapServersField.getAnnotation(org.springframework.beans.factory.annotation.Value).value() == 
            '${spring.kafka.producer.bootstrap-servers}'

        saslJaasConfigField.isAnnotationPresent(org.springframework.beans.factory.annotation.Value)
        saslJaasConfigField.getAnnotation(org.springframework.beans.factory.annotation.Value).value() == 
            '${spring.kafka.producer.sasl.jaas.config}'

        saslMechanismField.isAnnotationPresent(org.springframework.beans.factory.annotation.Value)
        saslMechanismField.getAnnotation(org.springframework.beans.factory.annotation.Value).value() == 
            '${spring.kafka.producer.sasl.mechanism}'

        securityProtocolField.isAnnotationPresent(org.springframework.beans.factory.annotation.Value)
        securityProtocolField.getAnnotation(org.springframework.beans.factory.annotation.Value).value() == 
            '${spring.kafka.producer.security.protocol}'
    }

    // Helper method to extract configs from producer factory
    private Map<String, Object> getProducerConfigs(DefaultKafkaProducerFactory factory) {
        def configsField = DefaultKafkaProducerFactory.class.getDeclaredField("configs")
        configsField.setAccessible(true)
        return configsField.get(factory) as Map<String, Object>
    }
}