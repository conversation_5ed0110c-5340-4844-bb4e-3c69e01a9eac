package com.ttb.top.dncupdatestreamsservice.configuration

import org.apache.kafka.common.serialization.StringDeserializer
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory
import org.springframework.kafka.core.ConsumerFactory
import org.springframework.kafka.core.DefaultKafkaConsumerFactory
import org.springframework.kafka.listener.ContainerProperties.AckMode
import spock.lang.Specification
import spock.lang.Subject
import spock.lang.Unroll

import java.lang.reflect.Modifier

class EventHubConsumerConfigTest extends Specification {

    @Subject
    EventHubConsumerConfig consumerConfig = new EventHubConsumerConfig()

    def setup() {
        // Set default values using reflection
        setPrivateField(consumerConfig, "eventHubPassword", "test-password")
        setPrivateField(consumerConfig, "consumerBootstrapAddress", "localhost:9092")
        setPrivateField(consumerConfig, "consumerProtocol", "PLAINTEXT")
        setPrivateField(consumerConfig, "consumerJaasConfig", "test-jaas-config-\${azure.eventhub.password}")
        setPrivateField(consumerConfig, "consumerMechanism", "PLAIN")
        //        setPrivateField(consumerConfig, "offsetReset", "latest")
        //        setPrivateField(consumerConfig, "enableAutoCommit", "true")
        //        setPrivateField(consumerConfig, "autoCommitInterval", "1000")
    }

    def "should have correct class annotations"() {
        expect: "class should be properly annotated"
        EventHubConsumerConfig.class.isAnnotationPresent(Configuration)
    }

    def "should verify field annotations"() {
        when: "checking field annotations"
        def eventHubPasswordField = EventHubConsumerConfig.class.getDeclaredField("eventHubPassword")
        def consumerBootstrapAddressField = EventHubConsumerConfig.class.getDeclaredField("consumerBootstrapAddress")
        def consumerProtocolField = EventHubConsumerConfig.class.getDeclaredField("consumerProtocol")
        def consumerJaasConfigField = EventHubConsumerConfig.class.getDeclaredField("consumerJaasConfig")
        def consumerMechanismField = EventHubConsumerConfig.class.getDeclaredField("consumerMechanism")

        then: "should have @Value annotations"
        eventHubPasswordField.isAnnotationPresent(Value)
        consumerBootstrapAddressField.isAnnotationPresent(Value)
        consumerProtocolField.isAnnotationPresent(Value)
        consumerJaasConfigField.isAnnotationPresent(Value)
        consumerMechanismField.isAnnotationPresent(Value)

        and: "should have correct property values"
        eventHubPasswordField.getAnnotation(Value).value() == '${azure.eventhub.password}'
        consumerBootstrapAddressField.getAnnotation(Value).value() == '${spring.kafka.consumer.bootstrap-servers}'
        consumerProtocolField.getAnnotation(Value).value() == '${spring.kafka.consumer.security.protocol}'
        consumerJaasConfigField.getAnnotation(Value).value() == '${spring.kafka.consumer.sasl.jaas.config}'
        consumerMechanismField.getAnnotation(Value).value() == '${spring.kafka.consumer.sasl.mechanism}'
    }

    def "should create ConsumerFactory with PLAINTEXT protocol"() {
        given: "PLAINTEXT protocol configuration"
        setPrivateField(consumerConfig, "consumerProtocol", "PLAINTEXT")

        when: "creating consumer factory"
        def result = consumerConfig.streamHelperConsumerFactory()

        then: "should return DefaultKafkaConsumerFactory"
        result != null
        result instanceof DefaultKafkaConsumerFactory

        and: "should have basic configuration without SASL"
        def configProps = result.getConfigurationProperties()
        configProps["bootstrap.servers"] == "localhost:9092"
        configProps["key.deserializer"] == StringDeserializer.class
        configProps["value.deserializer"] == StringDeserializer.class
        !configProps.containsKey("security.protocol")
        !configProps.containsKey("sasl.jaas.config")
        !configProps.containsKey("sasl.mechanism")
    }

    def "should create ConsumerFactory with SASL_SSL protocol"() {
        given: "SASL_SSL protocol configuration"
        setPrivateField(consumerConfig, "consumerProtocol", "SASL_SSL")
        setPrivateField(consumerConfig, "consumerJaasConfig", "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"test\" password=\"\${azure.eventhub.password}\";")
        setPrivateField(consumerConfig, "eventHubPassword", "secret-password")
        setPrivateField(consumerConfig, "consumerMechanism", "PLAIN")

        when: "creating consumer factory"
        def result = consumerConfig.streamHelperConsumerFactory()

        then: "should return DefaultKafkaConsumerFactory with SASL configuration"
        result != null
        result instanceof DefaultKafkaConsumerFactory

        and: "should have SASL_SSL configuration"
        def configProps = result.getConfigurationProperties()
        configProps["bootstrap.servers"] == "localhost:9092"
        configProps["key.deserializer"] == StringDeserializer.class
        configProps["value.deserializer"] == StringDeserializer.class
        configProps["security.protocol"] == "SASL_SSL"
        configProps["sasl.mechanism"] == "PLAIN"
        
        and: "should replace password placeholder in JAAS config"
        def jaasConfig = configProps["sasl.jaas.config"] as String
        jaasConfig.contains("secret-password")
        !jaasConfig.contains("\${azure.eventhub.password}")
    }

    def "should create KafkaListenerContainerFactory"() {
        when: "creating kafka listener container factory"
        def result = consumerConfig.kafkaListenerContainerFactory()

        then: "should return ConcurrentKafkaListenerContainerFactory"
        result != null
        result instanceof ConcurrentKafkaListenerContainerFactory

        and: "should have correct configuration"
        def factory = result as ConcurrentKafkaListenerContainerFactory
        factory.getContainerProperties().isStopImmediate()
    }


    def "should verify streamHelperConsumerFactory method annotations"() {
        when: "checking streamHelperConsumerFactory method annotations"
        def method = EventHubConsumerConfig.class.getMethod("streamHelperConsumerFactory")

        then: "should have correct annotations"
        method.isAnnotationPresent(Bean)
        method.isAnnotationPresent(ConditionalOnProperty)

        def conditionalAnnotation = method.getAnnotation(ConditionalOnProperty)
        conditionalAnnotation.value() == ["spring.kafka.consumer"] as String[]
        conditionalAnnotation.havingValue() == "enable"
    }

    def "should verify kafkaListenerContainerFactory method annotations"() {
        when: "checking kafkaListenerContainerFactory method annotations"
        def method = EventHubConsumerConfig.class.getMethod("kafkaListenerContainerFactory")

        then: "should have correct annotations"
        method.isAnnotationPresent(Bean)
        method.isAnnotationPresent(ConditionalOnProperty)
        method.isAnnotationPresent(ConditionalOnBean)

        def conditionalPropertyAnnotation = method.getAnnotation(ConditionalOnProperty)
        conditionalPropertyAnnotation.value() == ["spring.kafka.consumer"] as String[]
        conditionalPropertyAnnotation.havingValue() == "enable"

        def conditionalBeanAnnotation = method.getAnnotation(ConditionalOnBean)
        conditionalBeanAnnotation.value() == [ConsumerFactory.class] as Class[]
    }

    def "should handle password replacement in JAAS config"() {
        given: "JAAS config with password placeholder"
        setPrivateField(consumerConfig, "consumerProtocol", "SASL_SSL")
        setPrivateField(consumerConfig, "consumerJaasConfig", "LoginModule required username=\"user\" password=\"\${azure.eventhub.password}\";")
        setPrivateField(consumerConfig, "eventHubPassword", "my-secret-password")

        when: "creating consumer factory"
        def result = consumerConfig.streamHelperConsumerFactory()

        then: "should replace password placeholder"
        def configProps = result.getConfigurationProperties()
        def jaasConfig = configProps["sasl.jaas.config"] as String
        jaasConfig.contains("my-secret-password")
        !jaasConfig.contains("\${azure.eventhub.password}")
    }

    def "should handle multiple password placeholders in JAAS config"() {
        given: "JAAS config with multiple password placeholders"
        setPrivateField(consumerConfig, "consumerProtocol", "SASL_SSL")
        setPrivateField(consumerConfig, "consumerJaasConfig", "LoginModule required password=\"\${azure.eventhub.password}\" token=\"\${azure.eventhub.password}\";")
        setPrivateField(consumerConfig, "eventHubPassword", "test-password")

        when: "creating consumer factory"
        def result = consumerConfig.streamHelperConsumerFactory()

        then: "should replace all password placeholders"
        def configProps = result.getConfigurationProperties()
        def jaasConfig = configProps["sasl.jaas.config"] as String
        jaasConfig.contains("test-password")
        !jaasConfig.contains("\${azure.eventhub.password}")
        (jaasConfig =~ /test-password/).count == 2
    }

    def "should handle empty or null password"() {
        given: "empty password"
        setPrivateField(consumerConfig, "consumerProtocol", "SASL_SSL")
        setPrivateField(consumerConfig, "consumerJaasConfig", "LoginModule required password=\"\${azure.eventhub.password}\";")
        setPrivateField(consumerConfig, "eventHubPassword", "")

        when: "creating consumer factory"
        def result = consumerConfig.streamHelperConsumerFactory()

        then: "should replace with empty string"
        def configProps = result.getConfigurationProperties()
        def jaasConfig = configProps["sasl.jaas.config"] as String
        jaasConfig.contains("password=\"\"")
        !jaasConfig.contains("\${azure.eventhub.password}")
    }

    def "should handle null JAAS config"() {
        given: "null JAAS config"
        setPrivateField(consumerConfig, "consumerProtocol", "SASL_SSL")
        setPrivateField(consumerConfig, "consumerJaasConfig", null)
        setPrivateField(consumerConfig, "eventHubPassword", "password")

        when: "creating consumer factory"
        consumerConfig.streamHelperConsumerFactory()

        then: "should throw NullPointerException"
        thrown(NullPointerException)
    }

    def "should verify static constant"() {
        when: "checking static constant"
        def field = EventHubConsumerConfig.class.getDeclaredField("SASL_SSL_PROTOCOL")
        field.setAccessible(true)
        def value = field.get(null)

        then: "should have correct value"
        value == "SASL_SSL"
        Modifier.isStatic(field.modifiers)
        Modifier.isFinal(field.modifiers)
        Modifier.isPrivate(field.modifiers)
    }

    def "should handle different bootstrap server configurations"() {
        given: "different bootstrap server"
        setPrivateField(consumerConfig, "consumerBootstrapAddress", "kafka1:9092,kafka2:9092")

        when: "creating consumer factory"
        def result = consumerConfig.streamHelperConsumerFactory()

        then: "should use configured bootstrap servers"
        def configProps = result.getConfigurationProperties()
        configProps["bootstrap.servers"] == "kafka1:9092,kafka2:9092"
    }

    def "should verify consumer factory uses correct deserializers"() {
        when: "creating consumer factory"
        def result = consumerConfig.streamHelperConsumerFactory()

        then: "should use StringDeserializer for both key and value"
        def configProps = result.getConfigurationProperties()
        configProps["key.deserializer"] == StringDeserializer.class
        configProps["value.deserializer"] == StringDeserializer.class
    }

    def "should verify container properties configuration"() {
        when: "creating kafka listener container factory"
        def result = consumerConfig.kafkaListenerContainerFactory()

        then: "should configure container properties correctly"
        def factory = result as ConcurrentKafkaListenerContainerFactory
        def containerProps = factory.getContainerProperties()
        containerProps.isStopImmediate()
    }

    private void setPrivateField(Object target, String fieldName, Object value) {
        def field = target.class.getDeclaredField(fieldName)
        field.setAccessible(true)
        field.set(target, value)
    }
}
