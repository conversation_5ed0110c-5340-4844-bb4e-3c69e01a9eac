package com.ttb.top.dncupdatestreamsservice.mapper

import com.ttb.top.dncupdatestreamsservice.model.mongo.dncupdatephonefail.DncPhoneUpdateFailTransaction
import com.ttb.top.dncupdatestreamsservice.model.streams.producer.DncUpdateFail
import org.mapstruct.factory.Mappers
import spock.lang.Specification
import spock.lang.Subject

class DncUpdateFailMapperTest extends Specification {

    @Subject
    DncUpdateFailMapper mapper = Mappers.getMapper(DncUpdateFailMapper.class)

    def "should map DncPhoneUpdateFailTransaction.Body to DncUpdateFail.DncBody"() {
        given: "a DncPhoneUpdateFailTransaction.Body object"
        def phoneNumbers = ["1234567890", "0987654321"]
        def dncList = DncPhoneUpdateFailTransaction.DncList.builder()
                .action("ADD")
                .phoneNumbers(phoneNumbers)
                .expirationDateTime("2023-12-31T23:59:59Z")
                .build()
        def dncUpdatePhone = DncPhoneUpdateFailTransaction.Body.builder()
                .dncListId("list-123")
                .dncList(dncList)
                .build()

        when: "mapping to DncUpdateFail.DncBody"
        def result = mapper.mapDncUpdatePhoneToDncBody(dncUpdatePhone)

        then: "the mapped object should have the correct values"
        result != null
        result.dncListId == "list-123"
        result.dncList != null
        result.dncList.action == "ADD"
        result.dncList.phoneNumbers == phoneNumbers
        result.dncList.expirationDateTime == "2023-12-31T23:59:59Z"
    }

    def "should map DncPhoneUpdateFailTransaction.Error to DncUpdateFail.DncError"() {
        given: "a DncPhoneUpdateFailTransaction.Error object"
        def dncUpdateError = DncPhoneUpdateFailTransaction.Error.builder()
                .serviceName("test-service")
                .httpStatus("400")
                .code("9004")
                .message("Bad Request")
                .status("ERROR")
                .build()

        when: "mapping to DncUpdateFail.DncError"
        def result = mapper.mapDncUpdateErrorToDncError(dncUpdateError)

        then: "the mapped object should have the correct values"
        result != null
        result.serviceName == "test-service"
        result.httpStatus == "400"
        result.code == "9004"
        result.message == "Bad Request"
        result.status == "ERROR"
    }

    def "should map a list of DncPhoneUpdateFailTransaction.Body to a list of DncUpdateFail.DncBody"() {
        given: "a list of DncPhoneUpdateFailTransaction.Body objects"
        def phoneNumbers1 = ["1234567890"]
        def dncList1 = DncPhoneUpdateFailTransaction.DncList.builder()
                .action("ADD")
                .phoneNumbers(phoneNumbers1)
                .expirationDateTime("2023-12-31T23:59:59Z")
                .build()
        def dncUpdatePhone1 = DncPhoneUpdateFailTransaction.Body.builder()
                .dncListId("list-123")
                .dncList(dncList1)
                .build()

        def phoneNumbers2 = ["0987654321"]
        def dncList2 = DncPhoneUpdateFailTransaction.DncList.builder()
                .action("REMOVE")
                .phoneNumbers(phoneNumbers2)
                .expirationDateTime("2024-12-31T23:59:59Z")
                .build()
        def dncUpdatePhone2 = DncPhoneUpdateFailTransaction.Body.builder()
                .dncListId("list-456")
                .dncList(dncList2)
                .build()

        def dncUpdatePhoneList = [dncUpdatePhone1, dncUpdatePhone2]

        when: "mapping to a list of DncUpdateFail.DncBody"
        def result = mapper.mapDncUpdatePhoneListToDncBodyList(dncUpdatePhoneList)

        then: "the mapped list should have the correct size and values"
        result != null
        result.size() == 2
        
        result[0].dncListId == "list-123"
        result[0].dncList.action == "ADD"
        result[0].dncList.phoneNumbers == phoneNumbers1
        result[0].dncList.expirationDateTime == "2023-12-31T23:59:59Z"
        
        result[1].dncListId == "list-456"
        result[1].dncList.action == "REMOVE"
        result[1].dncList.phoneNumbers == phoneNumbers2
        result[1].dncList.expirationDateTime == "2024-12-31T23:59:59Z"
    }

    def "should map a list of DncPhoneUpdateFailTransaction.Error to a list of DncUpdateFail.DncError"() {
        given: "a list of DncPhoneUpdateFailTransaction.Error objects"
        def dncUpdateError1 = DncPhoneUpdateFailTransaction.Error.builder()
                .serviceName("test-service-1")
                .httpStatus("400")
                .code("9004")
                .message("Bad Request")
                .status("ERROR")
                .build()
                
        def dncUpdateError2 = DncPhoneUpdateFailTransaction.Error.builder()
                .serviceName("test-service-2")
                .httpStatus("500")
                .code("9005")
                .message("Internal Server Error")
                .status("FATAL")
                .build()

        def dncUpdateErrorList = [dncUpdateError1, dncUpdateError2]

        when: "mapping to a list of DncUpdateFail.DncError"
        def result = mapper.mapDncUpdateErrorListToDncErrorList(dncUpdateErrorList)

        then: "the mapped list should have the correct size and values"
        result != null
        result.size() == 2
        
        result[0].serviceName == "test-service-1"
        result[0].httpStatus == "400"
        result[0].code == "9004"
        result[0].message == "Bad Request"
        result[0].status == "ERROR"
        
        result[1].serviceName == "test-service-2"
        result[1].httpStatus == "500"
        result[1].code == "9005"
        result[1].message == "Internal Server Error"
        result[1].status == "FATAL"
    }

    def "should handle null inputs gracefully"() {
        when: "mapping null inputs"
        def bodyResult = mapper.mapDncUpdatePhoneToDncBody(null)
        def errorResult = mapper.mapDncUpdateErrorToDncError(null)
        def bodyListResult = mapper.mapDncUpdatePhoneListToDncBodyList(null)
        def errorListResult = mapper.mapDncUpdateErrorListToDncErrorList(null)

        then: "the results should be null"
        bodyResult == null
        errorResult == null
        bodyListResult == null
        errorListResult == null
    }

    def "should handle empty lists"() {
        given: "empty lists"
        def emptyBodyList = []
        def emptyErrorList = []

        when: "mapping empty lists"
        def bodyListResult = mapper.mapDncUpdatePhoneListToDncBodyList(emptyBodyList)
        def errorListResult = mapper.mapDncUpdateErrorListToDncErrorList(emptyErrorList)

        then: "the results should be empty lists"
        bodyListResult != null
        bodyListResult.isEmpty()
        errorListResult != null
        errorListResult.isEmpty()
    }

    def "should handle objects with null nested properties"() {
        given: "objects with null nested properties"
        def dncUpdatePhoneWithNullList = DncPhoneUpdateFailTransaction.Body.builder()
                .dncListId("list-123")
                .dncList(null)
                .build()

        def dncUpdateErrorWithNullFields = DncPhoneUpdateFailTransaction.Error.builder()
                .serviceName(null)
                .httpStatus(null)
                .code(null)
                .message(null)
                .status(null)
                .build()

        when: "mapping objects with null properties"
        def bodyResult = mapper.mapDncUpdatePhoneToDncBody(dncUpdatePhoneWithNullList)
        def errorResult = mapper.mapDncUpdateErrorToDncError(dncUpdateErrorWithNullFields)

        then: "the mapped objects should handle null properties gracefully"
        bodyResult != null
        bodyResult.dncListId == "list-123"
        bodyResult.dncList == null

        errorResult != null
        errorResult.serviceName == null
        errorResult.httpStatus == null
        errorResult.code == null
        errorResult.message == null
        errorResult.status == null
    }

    def "should handle DncList with null or empty phone numbers"() {
        given: "a DncList with null phone numbers"
        def dncListWithNullPhones = DncPhoneUpdateFailTransaction.DncList.builder()
                .action("ADD")
                .phoneNumbers(null)
                .expirationDateTime("2023-12-31T23:59:59Z")
                .build()
        def dncUpdatePhone = DncPhoneUpdateFailTransaction.Body.builder()
                .dncListId("list-123")
                .dncList(dncListWithNullPhones)
                .build()

        when: "mapping to DncUpdateFail.DncBody"
        def result = mapper.mapDncUpdatePhoneToDncBody(dncUpdatePhone)

        then: "the mapped object should handle null phone numbers"
        result != null
        result.dncListId == "list-123"
        result.dncList != null
        result.dncList.action == "ADD"
        result.dncList.phoneNumbers == null
        result.dncList.expirationDateTime == "2023-12-31T23:59:59Z"
    }

    def "should verify mapper instance is not null"() {
        expect: "mapper instance should be properly initialized"
        mapper != null
        DncUpdateFailMapper.INSTANCE != null
    }
}
