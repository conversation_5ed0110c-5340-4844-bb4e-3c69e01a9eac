package com.ttb.top.dncupdatestreamsservice.producer

import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.databind.ObjectMapper
import com.ttb.top.dncupdatestreamsservice.streams.producer.EventHubProducer
import org.springframework.kafka.core.KafkaTemplate
import org.springframework.kafka.support.SendResult
import spock.lang.Specification
import spock.lang.Subject
import java.util.concurrent.CancellationException
import java.util.concurrent.CompletableFuture
import java.util.concurrent.ExecutionException

class EventHubProducerTest extends Specification {

    KafkaTemplate<String, String> kafkaTemplate = Mock()
    ObjectMapper objectMapper = Mock()
    CompletableFuture<SendResult<String, String>> future = Mock()
    SendResult<String, String> sendResult = Mock()

    @Subject
    EventHubProducer<String> eventHubProducer

    def setup() {
        eventHubProducer = new EventHubProducer<>(kafkaTemplate, objectMapper)
    }

    def "should handle ExecutionException with cause"() {
        given: "a message to send"
        def data = "test-data"
        def serializedData = "serialized-test-data"
        def cause = new RuntimeException("Kafka error")
        def executionException = new ExecutionException(cause)

        and: "mocks are configured to throw ExecutionException"
        objectMapper.writeValueAsString(data) >> serializedData
        kafkaTemplate.send("test-topic", serializedData) >> future
        future.get() >> { throw executionException }

        when: "send is called"
        def result = eventHubProducer.send("test-topic", data)

        then: "kafkaTemplate.flush should be called"
        1 * kafkaTemplate.flush()

        and: "result should be false"
        result == false
    }

    def "should handle InterruptedException and restore interrupt flag"() {
        given: "a message to send"
        def data = "test-data"
        def serializedData = "serialized-test-data"
        def interruptedException = new InterruptedException("Thread interrupted")

        and: "mocks are configured to throw InterruptedException"
        objectMapper.writeValueAsString(data) >> serializedData
        kafkaTemplate.send("test-topic", serializedData) >> future
        future.get() >> { throw interruptedException }

        when: "send is called"
        def result = eventHubProducer.send("test-topic", data)

        then: "kafkaTemplate.flush should be called"
        1 * kafkaTemplate.flush()

        and: "result should be false"
        result == false
    }

    def "should handle CancellationException"() {
        given: "a message to send"
        def data = "test-data"
        def serializedData = "serialized-test-data"
        def cancellationException = new CancellationException("Send cancelled")

        and: "mocks are configured to throw CancellationException"
        objectMapper.writeValueAsString(data) >> serializedData
        kafkaTemplate.send("test-topic", serializedData) >> future
        future.get() >> { throw cancellationException }

        when: "send is called"
        def result = eventHubProducer.send("test-topic", data)

        then: "kafkaTemplate.flush should be called"
        1 * kafkaTemplate.flush()

        and: "result should be false"
        result == false
    }

    def "should handle generic Exception"() {
        given: "a message to send"
        def data = "test-data"
        def serializedData = "serialized-test-data"
        def genericException = new RuntimeException("Generic error")

        and: "mocks are configured to throw generic Exception"
        objectMapper.writeValueAsString(data) >> serializedData
        kafkaTemplate.send("test-topic", serializedData) >> future
        future.get() >> { throw genericException }

        when: "send is called"
        def result = eventHubProducer.send("test-topic", data)

        then: "kafkaTemplate.flush should be called"
        1 * kafkaTemplate.flush()

        and: "result should be false"
        result == false
    }

    def "should call flush after sending message"() {
        given: "a message to send"
        def data = "test-data"
        def serializedData = "serialized-test-data"
        def recordMetadata = Mock(org.apache.kafka.clients.producer.RecordMetadata)

        and: "mocks are configured for successful send"
        objectMapper.writeValueAsString(data) >> serializedData
        kafkaTemplate.send("test-topic", serializedData) >> future
        future.get() >> sendResult
        sendResult.getRecordMetadata() >> recordMetadata
        recordMetadata.partition() >> 1
        recordMetadata.offset() >> 100L

        when: "send is called"
        def result = eventHubProducer.send("test-topic", data)

        then: "kafkaTemplate.flush should be called"
        1 * kafkaTemplate.flush()

        and: "result should be true"
        result == true
    }

    def "should handle JsonProcessingException during serialization"() {
        given: "a message to send"
        def data = "test-data"
        def jsonProcessingException = new JsonProcessingException("Serialization failed") {}

        and: "mocks are configured to throw JsonProcessingException"
        objectMapper.writeValueAsString(data) >> { throw jsonProcessingException }

        when: "send is called"
        def result = eventHubProducer.send("test-topic", data)

        then: "result should be false"
        result == false

        and: "kafkaTemplate.send should not be called"
        0 * kafkaTemplate.send(_, _)

        and: "kafkaTemplate.flush should not be called"
        0 * kafkaTemplate.flush()
    }
}