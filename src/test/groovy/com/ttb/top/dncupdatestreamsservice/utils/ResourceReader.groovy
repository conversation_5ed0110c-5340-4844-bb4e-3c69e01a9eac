package com.ttb.top.dncupdatestreamsservice.utils

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule

class ResourceReader {
    def mapper = new ObjectMapper()
            .registerModule(new JavaTimeModule())
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)

    def <T> T readValue(String filePath, Class<T> valueType) {
        def inputStream = getClass().getClassLoader().getResourceAsStream(filePath)
        if (!inputStream) {
            throw new FileNotFoundException("File not found: $filePath")
        }
        return mapper.readValue(inputStream, valueType)
    }

    def <T> T readValue(String filePath, TypeReference<T> typeReference) {
        def inputStream = getClass().getClassLoader().getResourceAsStream(filePath)
        if (!inputStream) {
            throw new FileNotFoundException("File not found: $filePath")
        }
        return mapper.readValue(inputStream, typeReference)
    }
}
