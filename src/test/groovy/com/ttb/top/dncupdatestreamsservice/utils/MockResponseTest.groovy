package com.ttb.top.dncupdatestreamsservice.utils

import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.JavaType
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.ttb.top.library.commonmodel.model.ResponseModel
import com.ttb.top.library.commonmodel.model.ResponseStatus
import feign.FeignException
import feign.Request
import feign.RequestTemplate
import feign.Response
import org.apache.commons.lang3.StringUtils
import org.springframework.util.ResourceUtils

import java.net.http.HttpHeaders
import java.nio.charset.Charset

class MockResponseTest {
    static def getFeignResponseJson(String filename) {
        def filePath = StringUtils.join("classpath:", filename)
        def file = ResourceUtils.getFile(filePath)
        return new String(file.readBytes())
    }

    static <T> T parseJson(String jsonString, Class<T> clazz) {
        def mapper = new ObjectMapper().registerModule(new JavaTimeModule())
        mapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
        return mapper.readValue(jsonString, clazz)
    }

    static <T> T parseJson(String jsonString, Class<?> collectionClass, Class<?> elementClass) {
        def mapper = new ObjectMapper().registerModule(new JavaTimeModule())
        mapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)

        def javaType = mapper.typeFactory.constructCollectionType(collectionClass as Class<? extends Collection>, elementClass) as JavaType
        return mapper.readValue(jsonString, javaType)
    }

    static <T> ResponseModel<T> parseJsonWithRootKey(String jsonString, String rootKey, TypeReference<T> dataTypeRef) throws JsonProcessingException {
        def mapper = new ObjectMapper().registerModule(new JavaTimeModule())
        mapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);

        def rootNode = mapper.readTree(jsonString)

        def status = mapper.convertValue(rootNode.get("status"), ResponseStatus.class);

        def dataNode = rootNode.get(rootKey)
        T data = mapper.convertValue(dataNode, dataTypeRef)

        def responseModel = ResponseModel.success(data)
        responseModel.setStatus(status)

        return responseModel
    }

    static def createFeignException(int status, String body, Request.HttpMethod method) {
        def builder = Response.builder()
        builder.status(status)
        builder.reason("Reason")

        // Create a response
        def response = builder.request(Request.create(method, "url", Collections.emptyMap(), null, Charset.defaultCharset(), null))
                .body(body, Charset.defaultCharset())
                .build()

        // Create and return the FeignException
        return FeignException.errorStatus("MethodKey", response)
    }

    static def createFeignException(int status, String body, Request.HttpMethod method, HttpHeaders headers) {
        def builder = Response.builder()
        builder.status(status)
        builder.reason("Reason")

        // Create a response
        def response = builder.request(Request.create(method, "url", headers, null, Charset.defaultCharset(), null))
                .body(body, Charset.defaultCharset())
                .build()

        // Create and return the FeignException
        return FeignException.errorStatus("MethodKey", response)
    }

    static def throwFeignException(int status, String message, String bodyReq, String bodyRes, Request.HttpMethod method) {
        def headers = new HashMap<String, Collection<String>>()

        // Create a request
        def bodyReqToBodyType = StringUtils.isEmpty(bodyReq) ? null : Request.Body.create(bodyReq)
        def request = Request.create(method, "url", headers, bodyReqToBodyType, new RequestTemplate())

        // Create and return the FeignException
        throw new FeignException.FeignClientException(status, message, request, bodyRes.bytes, headers)
    }
}
