package com.ttb.top.dncupdatestreamsservice.utils

import io.github.resilience4j.circuitbreaker.CircuitBreaker

import static io.github.resilience4j.circuitbreaker.CallNotPermittedException.createCallNotPermittedException

class CircuitBreakerTest {
    static void fail() {
        def circuitBreaker = CircuitBreaker.ofDefaults("test")
        circuitBreaker.transitionToOpenState()

        throw createCallNotPermittedException(circuitBreaker)
    }
}
