package com.ttb.top.dncupdatestreamsservice.service

import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.core.type.TypeReference
import com.ttb.top.dncupdatestreamsservice.feign.customerdata.CustomerDataServiceFeignClient
import com.ttb.top.dncupdatestreamsservice.mapper.DncUpdateFailMapperImpl
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.customerprofile.request.CustomerProfileRequest
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.customerprofile.response.Customer
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.customerprofile.response.CustomerFeignExceptionResponse
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.updatephonenumbers.request.UpdatePhoneNumbersRequest
import com.ttb.top.dncupdatestreamsservice.model.mongo.dncupdatephonefail.DncPhoneUpdateFailTransaction
import com.ttb.top.dncupdatestreamsservice.model.streams.consumer.DncUpdateConsume
import com.ttb.top.dncupdatestreamsservice.model.streams.producer.DncUpdateFail
import com.ttb.top.dncupdatestreamsservice.model.streams.producer.DncUpdateFailProducer
import com.ttb.top.dncupdatestreamsservice.service.implement.DncUpdateConsumerServiceImpl
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.customerprofile.response.CustomerProfileResponse
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.customerprofile.response.Profile
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.customerprofile.response.Phone;
import com.ttb.top.dncupdatestreamsservice.streams.producer.EventHubProducer
import com.ttb.top.library.commonmodel.model.ResponseModel
import com.ttb.top.library.commonmodel.model.ResponseStatus
import feign.FeignException
import org.springframework.http.HttpHeaders
import spock.lang.Specification
import spock.lang.Subject
import java.util.concurrent.atomic.AtomicReference
import com.ttb.top.library.exceptionmodel.exception.GenericException
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.Logger

class DncUpdateConsumerServiceImplTest extends Specification {
    // Mock dependencies
    def objectMapper = Mock(ObjectMapper)
    def customerDataServiceFeignClient = Mock(CustomerDataServiceFeignClient)
    def customerCacheService = Mock(CustomerCacheService)
    def retryUpdateDncPhoneService = Mock(RetryUpdateDncPhoneService)
    def dncPhoneUpdateFailTransactionInsertService = Mock(DncPhoneUpdateFailTransactionInsertService)
    def producer = Mock(EventHubProducer)
    def dncUpdateFailMapper = new DncUpdateFailMapperImpl()
    
    @Subject
    def dncUpdateConsumerService
    
    def setup() {
        dncUpdateConsumerService = new DncUpdateConsumerServiceImpl(
            objectMapper,
            customerDataServiceFeignClient,
            customerCacheService,
            dncPhoneUpdateFailTransactionInsertService,
            retryUpdateDncPhoneService,
            producer,
            dncUpdateFailMapper
        )
    }
    
//    def "processMessage should handle message successfully when customer profile is in cache with no error"() {
//        given: "a valid message and acknowledgment"
//        def message = '{"headers":{"X-Correlation-ID":"test-id"},"dncUpdateConsumer":{"profile":{"rmId":"RM12345"},"body":[{"productGroupId":"PG001","dncList":{"action":"ADD","phoneNumbers":["0812345678"],"expirationDateTime":"2023-12-31T23:59:59"}}]}}'
//
//        and: "mocked data for the message processing"
//        def headers = new HttpHeaders();
//        headers.add("X-Correlation-ID", "test-id")
//
//        def dncUpdateConsume = DncUpdateConsume.builder()
//            .profile(DncUpdateConsume.Profile.builder().rmId("RM12345").build())
//            .body([
//                DncUpdateConsume.Body.builder()
//                    .productGroupId("PG001")
//                    .dncList(DncUpdateConsume.DncList.builder()
//                            .action("ADD")
//                            .phoneNumbers(["0812345678"])
//                            .expirationDateTime("2023-12-31T23:59:59")
//                            .build())
//                    .build()
//            ])
//            .build()
//
//        def customerProfileResponse = CustomerProfileResponse.builder()
//                .customer(Customer.builder()
//                    .profile(Profile.builder()
//                            .rmId("RM12345")
//                            .ccId("CC12345")
//                            .build())
//                    .phones([
//                        Phone.builder()
//                            .phoneType("M")
//                            .phoneNo("0812345678")
//                            .build()
//                    ])
//                    .build())
//                .build()
//
//        def updatePhoneNumbersRequest = UpdatePhoneNumbersRequest.builder()
//            .action("ADD")
//            .phoneNumbers(["0812345678"])
//            .expirationDateTime("2023-12-31T23:59:59")
//            .build()
//
//        DncUpdateFailProducer.DncUpdatePhone dp1 = DncUpdateFailProducer.DncUpdatePhone.builder()
//            .dncListId("PG001")
//            .dncList(DncUpdateFailProducer.DncList.builder()
//                .action("ADD")
//                .phoneNumbers(["0812345678"])
//                .expirationDateTime("2023-12-31T23:59:59")
//                .build())
//            .build()
//        DncUpdateFailProducer.DncUpdatePhone dp2 = DncUpdateFailProducer.DncUpdatePhone.builder()
//            .dncListId("PG002")
//            .dncList(DncUpdateFailProducer.DncList.builder()
//                .action("ADD")
//                .phoneNumbers(["0812345678"])
//                .expirationDateTime("2023-12-31T23:59:59")
//                .build())
//            .build()
//        List<DncUpdateFailProducer.DncUpdatePhone> body = new ArrayList<>();
//        body.add(dp1);
//        body.add(dp2);
//
//        DncUpdateFailProducer.DncUpdateError de1 = DncUpdateFailProducer.DncUpdateError.builder()
//            .httpStatus("500")
//            .code("9002")
//            .message("Internal Server Error")
//            .status("Internal Server Error")
//            .build();
//
//        DncUpdateFailProducer.DncUpdateError de2 = DncUpdateFailProducer.DncUpdateError.builder()
//            .httpStatus("500")
//            .code("9002")
//            .message("Internal Server Error")
//            .status("Internal Server Error")
//            .build();
//
//        List<DncUpdateFailProducer.DncUpdateError> errors = new ArrayList<>();
//        errors.add(de1);
//        errors.add(de2);
//
//        def dncUpdateFailProducer = DncUpdateFailProducer.builder()
//            .body(body)
//            .errors(errors)
//            .build()
//
//        and: "mock behavior for dependencies"
//        objectMapper.readValue(message, new TypeReference<HashMap<String, String>>() {}) >> [
//            "headers": '{"X-Correlation-ID":"test-id"}',
//            "dncUpdateConsumer": '{"profile":{"rmId":"RM12345"},"body":[{"productGroupId":"PG001","dncList":{"action":"ADD","phoneNumbers":["0812345678"],"expirationDateTime":"2023-12-31T23:59:59"}}]}'
//        ]
//        objectMapper.readValue('{"X-Correlation-ID":"test-id"}', HttpHeaders.class) >> headers
//        objectMapper.readValue('{"profile":{"rmId":"RM12345"},"body":[{"productGroupId":"PG001","dncList":{"action":"ADD","phoneNumbers":["0812345678"],"expirationDateTime":"2023-12-31T23:59:59"}}]}', DncUpdateConsume.class) >> dncUpdateConsume
//
//        customerCacheService.getCustomer("RM12345") >> customerProfileResponse
//
//        retryUpdateDncPhoneService.callUpdateDncPhoneWithRetry(
//            headers,
//            "PG001",
//            _ as UpdatePhoneNumbersRequest,
//            "RM12345",
//            "CC12345"
//        ) >> dncUpdateFailProducer
//
//        when: "processMessage is called"
//        dncUpdateConsumerService.processMessage(message)
//
//        then: "the message should be processed successfully"
//        1 * dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(
//            headers,
//            _ as DncPhoneUpdateFailTransaction,
//            "RM12345",
//            null
//        )
//    }

//    def "processMessage should handle message successfully when customer profile is in cache with error"() {
//        given: "a valid message and acknowledgment"
//        def message = '{"headers":{"X-Correlation-ID":"test-id"},"dncUpdateConsumer":{"profile":{"rmId":"RM12345"},"body":[{"productGroupId":"PG001","dncList":{"action":"ADD","phoneNumbers":["0812345678"],"expirationDateTime":"2023-12-31T23:59:59"}}]}}'
//
//        and: "mocked data for the message processing"
//        def headers = new HttpHeaders();
//        headers.add("X-Correlation-ID", "test-id")
//
//        def dncUpdateConsume = DncUpdateConsume.builder()
//                .profile(DncUpdateConsume.Profile.builder().rmId("RM12345").build())
//                .body([
//                        DncUpdateConsume.Body.builder()
//                                .productGroupId("PG001")
//                                .dncList(DncUpdateConsume.DncList.builder()
//                                        .action("ADD")
//                                        .phoneNumbers(["0812345678"])
//                                        .expirationDateTime("2023-12-31T23:59:59")
//                                        .build())
//                                .build()
//                ])
//                .build()
//
//        def customerProfileResponse = CustomerProfileResponse.builder()
//                .customer(Customer.builder()
//                        .profile(Profile.builder()
//                                .rmId("RM12345")
//                                .ccId("CC12345")
//                                .build())
//                        .phones([
//                                Phone.builder()
//                                        .phoneType("M")
//                                        .phoneNo("0812345678")
//                                        .build()
//                        ])
//                        .build())
//                .build()
//
//        def updatePhoneNumbersRequest = UpdatePhoneNumbersRequest.builder()
//                .action("ADD")
//                .phoneNumbers(["0812345678"])
//                .expirationDateTime("2023-12-31T23:59:59")
//                .build()
//
//        def dncUpdateFailProducer = DncUpdateFailProducer.builder()
//                .body([ DncUpdateFailProducer.DncUpdatePhone.builder()
//                        .dncListId("PG001")
//                        .dncList(DncUpdateFailProducer.DncList.builder()
//                                .action("ADD")
//                                .phoneNumbers(["0812345678"])
//                                .expirationDateTime("2023-12-31T23:59:59")
//                                .build())
//                        .build() ])
//                .errors([ DncUpdateFailProducer.DncUpdateError.builder()
//                        .httpStatus("500")
//                        .code("500")
//                        .message("Internal Server Error")
//                        .status("Internal Server Error")
//                        .build() ])
//                .build()
//
//        and: "mock behavior for dependencies"
//        objectMapper.readValue(message, new TypeReference<HashMap<String, String>>() {}) >> [
//                "headers": '{"X-Correlation-ID":"test-id"}',
//                "dncUpdateConsumer": '{"profile":{"rmId":"RM12345"},"body":[{"productGroupId":"PG001","dncList":{"action":"ADD","phoneNumbers":["0812345678"],"expirationDateTime":"2023-12-31T23:59:59"}}]}'
//        ]
//        objectMapper.readValue('{"X-Correlation-ID":"test-id"}', HttpHeaders.class) >> headers
//        objectMapper.readValue('{"profile":{"rmId":"RM12345"},"body":[{"productGroupId":"PG001","dncList":{"action":"ADD","phoneNumbers":["0812345678"],"expirationDateTime":"2023-12-31T23:59:59"}}]}', DncUpdateConsume.class) >> dncUpdateConsume
//
//        customerCacheService.getCustomer("RM12345") >> customerProfileResponse
//
//        retryUpdateDncPhoneService.callUpdateDncPhoneWithRetry(
//                headers,
//                "PG001",
//                _ as UpdatePhoneNumbersRequest,
//                "RM12345",
//                "CC12345"
//        ) >> dncUpdateFailProducer
//
//        when: "processMessage is called"
//        dncUpdateConsumerService.processMessage(message)
//
//        then: "the message should be processed successfully"
//        1 * dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(
//                headers,
//                _ as DncPhoneUpdateFailTransaction,
//                "RM12345",
//                null
//        )
//    }

//    def "processMessage should fetch customer profile when not in cache"() {
//        given: "a valid message and acknowledgment"
//        def message = '{"headers":{"X-Correlation-ID":"test-id"},"dncUpdateConsumer":{"profile":{"rmId":"RM12345"},"body":[{"productGroupId":"PG001","dncList":{"action":"ADD","phoneNumbers":["0812345678"],"expirationDateTime":"2023-12-31T23:59:59"}}]}}'
//
//        and: "mocked data for the message processing"
//        def headers = new HttpHeaders();
//        headers.add("X-Correlation-ID", "test-id")
//
//        def dncUpdateConsume = DncUpdateConsume.builder()
//            .profile(DncUpdateConsume.Profile.builder().rmId("RM12345").build())
//            .body([
//                DncUpdateConsume.Body.builder()
//                    .productGroupId("PG001")
//                    .dncList(DncUpdateConsume.DncList.builder()
//                        .action("ADD")
//                        .phoneNumbers(["0812345678"])
//                        .expirationDateTime("2023-12-31T23:59:59")
//                        .build())
//                    .build()
//            ])
//            .build()
//
//        def customerProfileResponse = CustomerProfileResponse.builder()
//            .customer(Customer.builder()
//                .profile(Profile.builder()
//                    .rmId("RM12345")
//                    .ccId("CC12345")
//                    .build())
//                .phones([
//                    Phone.builder()
//                        .phoneType("M")
//                        .phoneNo("0812345678")
//                        .build()
//                ])
//                .build())
//            .build()
//
//
//        def customerProfileResponseModel = new ResponseModel<>(
//            code: "0000",
//            dataObj: customerProfileResponse,
//            status: new ResponseStatus("0000")
//        )
//
//        def dncUpdateFailProducer = DncUpdateFailProducer.builder()
//            .body([ DncUpdateFailProducer.DncUpdatePhone.builder()
//                .dncListId("PG001")
//                .dncList(DncUpdateFailProducer.DncList.builder()
//                    .action("ADD")
//                    .phoneNumbers(["0812345678"])
//                    .expirationDateTime("2023-12-31T23:59:59")
//                    .build())
//                .build() ])
//            .errors([ DncUpdateFailProducer.DncUpdateError.builder()
//                .httpStatus("500")
//                .code("500")
//                .message("Internal Server Error")
//                .status("Internal Server Error")
//                .build() ])
//            .build()
//
//        and: "mock behavior for dependencies"
//        objectMapper.readValue(message, new TypeReference<HashMap<String, String>>() {}) >> [
//            "headers": '{"X-Correlation-ID":"test-id"}',
//            "dncUpdateConsumer": '{"profile":{"rmId":"RM12345"},"body":[{"productGroupId":"PG001","dncList":{"action":"ADD","phoneNumbers":["0812345678"],"expirationDateTime":"2023-12-31T23:59:59"}}]}'
//        ]
//        objectMapper.readValue('{"X-Correlation-ID":"test-id"}', HttpHeaders.class) >> headers
//        objectMapper.readValue('{"profile":{"rmId":"RM12345"},"body":[{"productGroupId":"PG001","dncList":{"action":"ADD","phoneNumbers":["0812345678"],"expirationDateTime":"2023-12-31T23:59:59"}}]}', DncUpdateConsume.class) >> dncUpdateConsume
//
//        customerCacheService.getCustomer("RM12345") >> null
//        customerDataServiceFeignClient.customerProfile(headers, _ as CustomerProfileRequest) >> customerProfileResponseModel
//
//        retryUpdateDncPhoneService.callUpdateDncPhoneWithRetry(
//            headers,
//            "PG001",
//            _ as UpdatePhoneNumbersRequest,
//            "RM12345",
//            "CC12345"
//        ) >> dncUpdateFailProducer
//
//        when: "processMessage is called"
//        dncUpdateConsumerService.processMessage(message)
//
//        then: "the customer profile should be fetched and cached"
//        1 * customerCacheService.setCustomer("RM12345", customerProfileResponse)
//        1 * dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(
//            headers,
//            _ as DncPhoneUpdateFailTransaction,
//            "RM12345",
//            null
//        )
//
//    }

    def "processMessage should handle customer profile fetch error and insert mongo"() {
        given: "a valid message and acknowledgment"
        def message = '{"headers":{"X-Correlation-ID":"test-id"},"dncUpdateConsumer":{"profile":{"rmId":"RM12345"},"body":[{"productGroupId":"PG001","dncList":{"action":"ADD","phoneNumbers":["0812345678"],"expirationDateTime":"2023-12-31T23:59:59"}}]}}'

        and: "mocked data for the message processing"
        def headers = new HttpHeaders();
        headers.add("X-Correlation-ID", "test-id")

        def dncUpdateConsume = DncUpdateConsume.builder()
            .profile(DncUpdateConsume.Profile.builder().rmId("RM12345").build())
            .body([
                DncUpdateConsume.Body.builder()
                    .productGroupId("PG001")
                    .dncList(DncUpdateConsume.DncList.builder()
                        .action("ADD")
                        .phoneNumbers(["0812345678"])
                        .expirationDateTime("2023-12-31T23:59:59")
                        .build())
                    .build()
            ])
            .build()

        def customerFeignExceptionResponse = CustomerFeignExceptionResponse.builder()
            .status(CustomerFeignExceptionResponse.CustomerProfileError.builder()
                .code("9999")
                .description("Request Timeout")
                .build())
            .build()

        def dncUpdateFailProducer = DncUpdateFailProducer.builder()
            .body([DncUpdateFailProducer.DncUpdatePhone.builder()
                           .dncListId("PG001")
                           .dncList(DncUpdateFailProducer.DncList.builder()
                                   .action("ADD")
                                   .phoneNumbers(["0812345678"])
                                   .expirationDateTime("2023-12-31T23:59:59")
                                   .build())
                           .build()])
            .errors([DncUpdateFailProducer.DncUpdateError.builder().build()])
            .build()

        def mockedFeignException = Mock(FeignException)

        and: "mock behavior for dependencies"
        objectMapper.readValue(message, new TypeReference<HashMap<String, String>>() {}) >> [
            "headers": '{"X-Correlation-ID":"test-id"}',
            "dncUpdateConsumer": '{"profile":{"rmId":"RM12345"},"body":[{"productGroupId":"PG001","dncList":{"action":"ADD","phoneNumbers":["0812345678"],"expirationDateTime":"2023-12-31T23:59:59"}}]}'
        ]
        objectMapper.readValue('{"X-Correlation-ID":"test-id"}', HttpHeaders.class) >> headers
        objectMapper.readValue('{"profile":{"rmId":"RM12345"},"body":[{"productGroupId":"PG001","dncList":{"action":"ADD","phoneNumbers":["0812345678"],"expirationDateTime":"2023-12-31T23:59:59"}}]}', DncUpdateConsume.class) >> dncUpdateConsume
        objectMapper.readValue('{"status": {"code": "9002","header": "","description": "Request Timeout"},"data": null}', CustomerFeignExceptionResponse.class) >> customerFeignExceptionResponse

        customerCacheService.getCustomer("RM12345") >> null
        customerDataServiceFeignClient.customerProfile(_, _) >>  { throw mockedFeignException }
        retryUpdateDncPhoneService.callUpdateDncPhoneWithRetry(_, _, _, _, _) >> dncUpdateFailProducer

        when: "processMessage is called"
        mockedFeignException.contentUTF8() >> '{"status": {"code": "9002","header": "","description": "Request Timeout"},"data": null}'
        producer.send(_, _) >> true
        dncUpdateConsumerService.processMessage(message)

        then: "the error should be handled and transaction inserted"
        1 * dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(
            headers,
            { DncPhoneUpdateFailTransaction transaction ->
                transaction.dncError.size() == 1 &&
                transaction.dncBody.size() == 1
            },
            "RM12345",
            null
        )
    }

    def "processMessage should handle permanent expiration date"() {
        given: "a valid message with permanent expiration date"
        def message = '{"headers":{"X-Correlation-ID":"test-id"},"dncUpdateConsumer":{"profile":{"rmId":"RM12345"},"body":[{"productGroupId":"PG001","dncList":{"action":"ADD","phoneNumbers":["+66812345678"],"expirationDateTime":"4000-12-31T23:59Z"}}]}}'

        and: "mocked data for the message processing"
        def headers = new HttpHeaders();
        headers.add("X-Correlation-ID", "test-id")


        def dncUpdateConsume = DncUpdateConsume.builder()
            .profile(DncUpdateConsume.Profile.builder().rmId("RM12345").build())
            .body([
                DncUpdateConsume.Body.builder()
                    .productGroupId("PG001")
                    .dncList(DncUpdateConsume.DncList.builder()
                        .action("ADD")
                        .phoneNumbers(["+66812345678"])
                        .expirationDateTime("4000-12-31T23:59Z")
                        .build())
                    .build()
            ])
            .build()

        def customerProfileResponse = CustomerProfileResponse.builder()
            .customer(Customer.builder()
                .profile(Profile.builder()
                    .rmId("RM12345")
                    .ccId("CC12345")
                    .build())
                .phones([
                    Phone.builder()
                        .phoneType("M")
                        .phoneNoFull("+66812345678")
                        .build()
                ])
                .build())
            .build()

        def dncUpdateFailProducer = DncUpdateFailProducer.builder()
            .body([ DncUpdateFailProducer.DncUpdatePhone.builder()
                .dncListId("PG001")
                .dncList(DncUpdateFailProducer.DncList.builder()
                    .action("ADD")
                    .phoneNumbers(["+66812345678"])
                    .expirationDateTime(null)
                    .build())
                .build() ])
            .errors([ DncUpdateFailProducer.DncUpdateError.builder()
                .httpStatus("500")
                .code("500")
                .message("Internal Server Error")
                .status("Internal Server Error")
                .build() ])
            .build()

        and: "mock behavior for dependencies"
        objectMapper.readValue(message, new TypeReference<HashMap<String, String>>() {}) >> [
            "headers": '{"X-Correlation-ID":"test-id"}',
            "dncUpdateConsumer": '{"profile":{"rmId":"RM12345"},"body":[{"productGroupId":"PG001","dncList":{"action":"ADD","phoneNumbers":["0812345678"],"expirationDateTime":"4000-12-31T23:59Z"}}]}'
        ]
        objectMapper.readValue('{"X-Correlation-ID":"test-id"}', HttpHeaders.class) >> headers
        objectMapper.readValue('{"profile":{"rmId":"RM12345"},"body":[{"productGroupId":"PG001","dncList":{"action":"ADD","phoneNumbers":["0812345678"],"expirationDateTime":"4000-12-31T23:59Z"}}]}', DncUpdateConsume.class) >> dncUpdateConsume

        customerCacheService.getCustomer("RM12345") >> customerProfileResponse

        retryUpdateDncPhoneService.callUpdateDncPhoneWithRetry(
            headers,
            "PG001",
            { UpdatePhoneNumbersRequest req ->
                req.action == "ADD" &&
                req.phoneNumbers == ["+66812345678"] &&
                req.expirationDateTime == null
            },
            "RM12345",
            "CC12345"
        ) >> dncUpdateFailProducer

        producer.send(_, _) >> true

        when: "processMessage is called"
        dncUpdateConsumerService.processMessage(message)

        then: "the request should be processed with null expirationDateTime"
        1 * dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(
            headers,
            _ as DncPhoneUpdateFailTransaction,
            "RM12345",
            null
        )
   }
    
    def "processMessage should handle JsonProcessingException"() {
        given: "a message that causes JsonProcessingException"
        def message = "invalid json"

        and: "mock behavior for dependencies"
        objectMapper.readValue(message, new TypeReference<HashMap<String, String>>() {}) >> { 
            throw new JsonProcessingException("Invalid JSON") {} 
        }
        
        when: "processMessage is called"
        dncUpdateConsumerService.processMessage(message)
        
        then: "GenericException should be thrown"
        thrown(GenericException)
    }

    def "should publish to eventhub when a feign fail producer contains error code 9004"() {
        given: "A feign failure with a 9004 error"
        def isFeignFail = new AtomicReference<Boolean>(true)
        def headers = new HttpHeaders()
        headers.add("x-correlation-id", "test-correlation-123")

        def dncUpdateConsume =  DncUpdateConsume.builder()
                .profile(DncUpdateConsume.Profile.builder().rmId("RM123").build())
                .body([DncUpdateFailProducer.DncUpdatePhone.builder().dncListId("PG001").build()])
                .build()

        def errorToTest = DncUpdateFailProducer.DncUpdateError.builder()
                .code("9004")
                .message("System unavailable")
                .status("E")
                .httpStatus("503")
                .build()

        def dncUpdateFailProducer = DncUpdateFailProducer.builder()
                .body([DncUpdateFailProducer.DncUpdatePhone.builder()
                               .dncListId("PG001")
                               .dncList(DncUpdateFailProducer.DncList.builder()
                                       .action("ADD")
                                       .phoneNumbers(["0812345678"])
                                       .expirationDateTime("2023-12-31T23:59:59")
                                       .build())
                               .build()])
                .errors([errorToTest])
                .build()

        def dncUpdateFailList = [dncUpdateFailProducer]

        and: "Mappers are set up to return mapped objects"
        def mappedBody = [DncUpdateFail.DncBody.builder().build()]
        def mappedError = [DncUpdateFail.builder().build()]
        dncUpdateFailMapper.mapDncUpdatePhoneListToDncBodyList(_) >> mappedBody
        dncUpdateFailMapper.mapDncUpdateErrorListToDncErrorList(_) >> mappedError

        and: "The producer is expected to be successful"
        def expectedDncUpdateFail = DncUpdateFail.builder().body(mappedBody).error(mappedError).build()
        def expectedJsonPayload = '{"body": [], "error": []}'
        objectMapper.writeValueAsString(expectedDncUpdateFail) >> expectedJsonPayload
        producer.send(_, _) >> true


        when: "the feign failure is checked"
        // Assuming checkFeignFail is made protected or package-private for testing
        dncUpdateConsumerService.checkFeignFail(isFeignFail, dncUpdateFailProducer, headers, dncUpdateConsume, dncUpdateFailList)

        then: "the failed transaction is inserted into the database"
        1 * dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(
                headers,
                _ as DncPhoneUpdateFailTransaction,
                "RM123",
                null
        )
    }

    def "should log successfully when objectMapper works correctly"() {
        given:
        def xcorrelation = "test-correlation-id"
        def httpStatusCode = "200"
        def action = "Published"
        def url = "test-url"
        def request = [key: "value"]
        def response = true

        when:
        dncUpdateConsumerService.applicationLog(xcorrelation, httpStatusCode, action, url, request, response)

        then:
        noExceptionThrown()
    }

    def "should log successfully when objectMapper throw JsonProcessingException"() {
        given: "a message that causes JsonProcessingException"
        def message = "invalid json"
        def xcorrelation = "test-correlation-id"
        def httpStatusCode = "200"
        def action = "Published"
        def url = "test-url"
        def request = [key: "value"]
        def response = true

        and: "mock behavior for dependencies"
        objectMapper.writeValueAsString(_) >> {
            throw new JsonProcessingException("Invalid JSON") {}
        }

        when: "processMessage is called"
        dncUpdateConsumerService.applicationLog(xcorrelation, httpStatusCode, action, url, request, response)

        then: "JsonProcessingException should catch and no exception should be thrown"
        noExceptionThrown()
    }

    def "handleException should handle null exception message"() {
        given: "an exception with null message and DncUpdateConsume object"
        def exception = new NullPointerException()
        def dncUpdateConsume = DncUpdateConsume.builder()
                .profile(DncUpdateConsume.Profile.builder().rmId("RM12345").build())
                .body([
                        DncUpdateConsume.Body.builder()
                                .productGroupId("PG001")
                                .dncList(DncUpdateConsume.DncList.builder()
                                        .action("ADD")
                                        .phoneNumbers(["0812345678"])
                                        .build())
                                .build()
                ])
                .build()

        when: "handleException is called"
        def result = dncUpdateConsumerService.handleException(exception, dncUpdateConsume)

        then: "should handle null message gracefully"
        result != null
        result.dncError[0].message == null
    }
}
