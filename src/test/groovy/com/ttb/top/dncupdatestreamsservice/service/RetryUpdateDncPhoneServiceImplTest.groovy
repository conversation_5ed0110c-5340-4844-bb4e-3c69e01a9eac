package com.ttb.top.dncupdatestreamsservice.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.ttb.top.dncupdatestreamsservice.exception.RetryUpdateDncPhoneException
import com.ttb.top.dncupdatestreamsservice.feign.customerdata.CustomerDataServiceFeignClient
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.getproductgroupid.response.ProductGroupIdResponse
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.updatephonenumbers.request.UpdatePhoneNumbersRequest
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.updatephonenumbers.response.UpdatePhoneNumbersResponse
import com.ttb.top.dncupdatestreamsservice.model.mongo.dncupdatephonefail.DncPhoneUpdateFailTransaction
import com.ttb.top.dncupdatestreamsservice.utils.MockResponseTest
import feign.Request
import groovy.json.JsonOutput
import org.springframework.aop.framework.AopContext
import com.ttb.top.dncupdatestreamsservice.service.implement.DncPhoneUpdateFailTransactionInsertServiceImpl
import com.ttb.top.dncupdatestreamsservice.service.implement.RetryUpdateDncPhoneServiceImpl
import com.ttb.top.library.commonmodel.model.ResponseModel
import com.ttb.top.library.commonmodel.model.ResponseStatus
import feign.FeignException
import org.springframework.http.HttpHeaders
import spock.lang.Specification
import spock.lang.Subject
import org.mockito.Mockito
import org.springframework.retry.RetryContext
import org.springframework.retry.support.RetrySynchronizationManager
import com.ttb.top.dncupdatestreamsservice.utils.CircuitBreakerTest

class RetryUpdateDncPhoneServiceImplTest extends Specification {
    // Mock dependencies
    def customerDataServiceFeignClient = Mock(CustomerDataServiceFeignClient)
    def phoneUpdateFailTransactionInsertService = Mock(DncPhoneUpdateFailTransactionInsertServiceImpl)
    def dncPhoneUpdateFailTransactionInsertService = Mock(DncPhoneUpdateFailTransactionInsertService)
    
    // Test data
    def headers = new HttpHeaders()
    def productGroupId = "PG12345"
    def updateRequest = new UpdatePhoneNumbersRequest(
        action: "ADD",
        phoneNumbers: ["0812345678"],
        expirationDateTime: "2023-12-31T23:59:59"
    )
    def rmId = "RM12345"
    def ecId = "EC12345"

    def proxyMock = Mock(RetryUpdateDncPhoneService)
    def mockedAopContext = Mockito.mockStatic(AopContext)
    def mockedRetrySyncManager = Mockito.mockStatic(RetrySynchronizationManager)

    @Subject
    RetryUpdateDncPhoneServiceImpl service
    RetryUpdateDncPhoneServiceImpl serviceSpy

    def setup() {
        service = new RetryUpdateDncPhoneServiceImpl(
            customerDataServiceFeignClient,
            phoneUpdateFailTransactionInsertService,
            dncPhoneUpdateFailTransactionInsertService,
            new ObjectMapper(),
            3,
            "ln:17cd0894-3fa6-4d5d-b8e8-bd43b736daf3|inv:d7ddebd7-207a-4f85-904c-24ae9195b666"
        )

        serviceSpy = Spy(RetryUpdateDncPhoneServiceImpl, constructorArgs: [
                customerDataServiceFeignClient,
                phoneUpdateFailTransactionInsertService,
                dncPhoneUpdateFailTransactionInsertService,
                new ObjectMapper(),
                3,
                "ln:17cd0894-3fa6-4d5d-b8e8-bd43b736daf3|inv:d7ddebd7-207a-4f85-904c-24ae9195b666"
        ])


        headers.add("X-Correlation-ID", "test-correlation-id")
        mockedAopContext.when({ AopContext.currentProxy() }).thenReturn(proxyMock)
    }

    def cleanup() {
        mockedAopContext.close()
        mockedRetrySyncManager.close()
    }
    
    def "updateDncPhoneWithRetry should return success response when status code is 0000"() {
        given: "a successful response from the client"
        def successResponse = createSuccessResponse()
        customerDataServiceFeignClient.updatePhoneNumbers(headers, productGroupId, updateRequest) >> successResponse
        
        when: "the retry method is called"
        def result = service.updateDncPhoneWithRetry(headers, productGroupId, updateRequest, rmId, ecId)
        
        then: "the result should be the success response"
        result == successResponse
        result.status.code == "0000"
    }

    def "updateDncPhoneWithRetry should handle FeignException and throw RetryUpdateDncPhoneException"() {
        given: "a FeignException from the client"
        def feignException = Mock(FeignException)
        feignException.contentUTF8() >> "{\"status\":{\"code\":\"9004\",\"header\":\"\"," +
                "\"description\":\"\"},\"data\":{\"customAppError\":{\"httpStatus\":\"401\",\"code\":\"\"," +
                "\"message\":\"Unauthorized\",\"status\":\"401\"}}}"

        customerDataServiceFeignClient.updatePhoneNumbers(headers, productGroupId, updateRequest) >> { throw feignException }

        // mock retryAttempt = 1
        def mockContext = Mockito.mock(RetryContext)
        Mockito.when(mockContext.getRetryCount()).thenReturn(1)
        mockedRetrySyncManager.when({ RetrySynchronizationManager.getContext() }).thenReturn(mockContext)

        when: "the retry method is called"
        service.updateDncPhoneWithRetry(headers, productGroupId, updateRequest, rmId, ecId)

        then: "expect retry exception"
        thrown(RetryUpdateDncPhoneException)
    }


    def "updateDncPhoneWithRetry should handle FeignException and insert fail transaction when max attempts reached"() {
        given: "a FeignException from the client"
        def feignException = Mock(FeignException)
        feignException.contentUTF8() >> "{\"status\":{\"code\":\"9004\",\"header\":\"\"," +
                "\"description\":\"\"},\"data\":{\"customAppError\":{\"httpStatus\":\"401\",\"code\":\"\"," +
                "\"message\":\"Unauthorized\",\"status\":\"401\"}}}"

        customerDataServiceFeignClient.updatePhoneNumbers(headers, productGroupId, updateRequest) >> { throw feignException }

        // mock retryAttempt = 4
        def mockContext = Mockito.mock(RetryContext)
        Mockito.when(mockContext.getRetryCount()).thenReturn(4)
        mockedRetrySyncManager.when({ RetrySynchronizationManager.getContext() }).thenReturn(mockContext)

        when: "the retry method is called"
        def result = service.updateDncPhoneWithRetry(headers, productGroupId, updateRequest, rmId, ecId)

        then: "the result should be null since no response was set"
        result.status.code == "9004"
    }
    
    def "callUpdateDncPhoneWithRetry should return empty producer on successful update"() {
        given: "a successful response from updateDncPhoneWithRetry"
        def successResponse = createSuccessResponse()
        customerDataServiceFeignClient.updatePhoneNumbers(headers, productGroupId, updateRequest) >> successResponse

        mockedAopContext.when({ AopContext.currentProxy() }).thenReturn(proxyMock)
        proxyMock.updateDncPhoneWithRetry(headers, productGroupId, updateRequest, rmId, ecId) >> successResponse
        
        when: "callUpdateDncPhoneWithRetry is called"
        def result = service.callUpdateDncPhoneWithRetry(headers, productGroupId, updateRequest, rmId, ecId)
        
        then: "an empty DncUpdateFailProducer should be returned"
        result != null
        result.body.isEmpty()
        result.errors.isEmpty()
    }

    def "callUpdateDncPhoneWithRetry should build error producer when update fails and getProductGroupId fails 1"() {
        given: "an error response from updateDncPhoneWithRetry"
        def errorResponse = createCustomAppErrorNullErrorResponse();
        serviceSpy.updateDncPhoneWithRetry(_, _, _, _, _) >> errorResponse
        customerDataServiceFeignClient.updatePhoneNumbers(headers, productGroupId, updateRequest) >> errorResponse
        proxyMock.updateDncPhoneWithRetry(headers, productGroupId, updateRequest, rmId, ecId) >> errorResponse
        serviceSpy.updateDncPhoneWithRetry(_, _, _, _, _) >> proxyMock

        and: "a successful product group ID response"
        def pgIdResponse = new ResponseModel<ProductGroupIdResponse>(
                dataObj: null,
                status:  new ResponseStatus("9004")
        )

        customerDataServiceFeignClient.getProductGroupId(headers, productGroupId) >> pgIdResponse

        when: "callUpdateDncPhoneWithRetry is called"
        def result = serviceSpy.callUpdateDncPhoneWithRetry(headers, productGroupId, updateRequest, rmId, ecId)

        then: "a DncUpdateFailProducer with error details should be returned"
        result != null

    }

    def "callUpdateDncPhoneWithRetry should build error producer when update fails and getProductGroupId fails 2"() {
        given: "an error response from updateDncPhoneWithRetry"
        def errorResponse = null
        serviceSpy.updateDncPhoneWithRetry(_, _, _, _, _) >> errorResponse
        customerDataServiceFeignClient.updatePhoneNumbers(headers, productGroupId, updateRequest) >> errorResponse
        proxyMock.updateDncPhoneWithRetry(headers, productGroupId, updateRequest, rmId, ecId) >> errorResponse
        serviceSpy.updateDncPhoneWithRetry(_, _, _, _, _) >> proxyMock

        and: "a successful product group ID response"
        def pgIdResponse = new ResponseModel<ProductGroupIdResponse>(
                dataObj: null,
                status:  new ResponseStatus("9004")
        )

        customerDataServiceFeignClient.getProductGroupId(headers, productGroupId) >> pgIdResponse

        when: "callUpdateDncPhoneWithRetry is called"
        def result = serviceSpy.callUpdateDncPhoneWithRetry(headers, productGroupId, updateRequest, rmId, ecId)

        then: "a DncUpdateFailProducer with error details should be returned"
        result != null

    }
    
    def "callUpdateDncPhoneWithRetry should build error producer when update fails"() {
        given: "an error response from updateDncPhoneWithRetry"
        def errorResponse = createErrorResponse("E001", "VALIDATION_ERR", "Validation Error")
        serviceSpy.updateDncPhoneWithRetry(_, _, _, _, _) >> errorResponse
        customerDataServiceFeignClient.updatePhoneNumbers(headers, productGroupId, updateRequest) >> errorResponse
        proxyMock.updateDncPhoneWithRetry(headers, productGroupId, updateRequest, rmId, ecId) >> errorResponse
        serviceSpy.updateDncPhoneWithRetry(_, _, _, _, _) >> proxyMock

        and: "a successful product group ID response"
        def pgIdResponse = new ResponseModel<ProductGroupIdResponse>(
            dataObj: new ProductGroupIdResponse(dncListId: productGroupId),
            status:  new ResponseStatus("9004")
        )

        customerDataServiceFeignClient.getProductGroupId(headers, productGroupId) >> pgIdResponse
        
        when: "callUpdateDncPhoneWithRetry is called"
        def result = serviceSpy.callUpdateDncPhoneWithRetry(headers, productGroupId, updateRequest, rmId, ecId)
        
        then: "a DncUpdateFailProducer with error details should be returned"
        result != null
        !result.body.isEmpty()
        result.body.size() == 1
        result.body[0].dncListId == productGroupId
        !result.errors.isEmpty()
        result.errors.size() == 1
        result.errors[0].code == "9004"
        result.errors[0].message == "Validation Error"
    }

    def "callUpdateDncPhoneWithRetry should build error when get config fails"() {
        given: "an error response from updateDncPhoneWithRetry"
        def errorResponse = createErrorResponse("E001", "VALIDATION_ERR", "Validation Error")
        serviceSpy.updateDncPhoneWithRetry(_, _, _, _, _) >> errorResponse
        customerDataServiceFeignClient.updatePhoneNumbers(headers, productGroupId, updateRequest) >> errorResponse
        proxyMock.updateDncPhoneWithRetry(headers, productGroupId, updateRequest, rmId, ecId) >> errorResponse

        and: "a successful product group ID response"
        def pgIdResponse = new ResponseModel<ProductGroupIdResponse>(
                dataObj: null,
                status:  new ResponseStatus("9004")
        )

        customerDataServiceFeignClient.getProductGroupId(headers, productGroupId) >> pgIdResponse

        when: "callUpdateDncPhoneWithRetry is called"
        def result = serviceSpy.callUpdateDncPhoneWithRetry(headers, productGroupId, updateRequest, rmId, ecId)

        then: "a DncUpdateFailProducer with error details should be returned"
        result != null
        result.body.isEmpty()
        !result.errors.isEmpty()
        result.errors.size() == 1
        result.errors[0].code == "9004"
        result.errors[0].message == "Validation Error"
    }

    def "callUpdateDncPhoneWithRetry should build error when getProductGroupId throws feign exception"() {
        given: "an error response from updateDncPhoneWithRetry"
        def errorResponse = createErrorResponse("E001", "VALIDATION_ERR", "Validation Error")
        serviceSpy.updateDncPhoneWithRetry(_, _, _, _, _) >> errorResponse
        customerDataServiceFeignClient.updatePhoneNumbers(headers, productGroupId, updateRequest) >> errorResponse
        proxyMock.updateDncPhoneWithRetry(headers, productGroupId, updateRequest, rmId, ecId) >> errorResponse

        and: "a successful product group ID response"
        def responseBody = JsonOutput.toJson(
                "status": ["code": "9000",
                           "header": "Error",
                           "description": "Error"]

        )

        customerDataServiceFeignClient.getProductGroupId(headers, productGroupId) >> {
            throw MockResponseTest.throwFeignException(
                    500,
                    "Internal Server Error",
                    null,
                    responseBody,
                    Request.HttpMethod.POST
            )
        }

        when: "callUpdateDncPhoneWithRetry is called"
        def result = serviceSpy.callUpdateDncPhoneWithRetry(headers, productGroupId, updateRequest, rmId, ecId)

        then: "a DncUpdateFailProducer with error details should be returned"
        result != null
        result.body.isEmpty()
        !result.errors.isEmpty()
        result.errors.size() == 1
        result.errors[0].code == "9004"
        result.errors[0].message == "Validation Error"
    }
    
    def "recoverFail should return the lastResponse from the exception"() {
        given: "a RetryUpdateDncPhoneException with a response"
        def errorResponse = createErrorResponse("E999", "RETRY_FAIL", "Retry Exhausted")
        def exception = new RetryUpdateDncPhoneException(
            "Test retry exception", updateRequest, errorResponse, productGroupId, headers
        )
        
        when: "recoverFail is called"
        def result = service.recoverFail(exception, headers, productGroupId, updateRequest, rmId, ecId)
        
        then: "the lastResponse from the exception should be returned"
        result == errorResponse
    }

    def "callUpdateDncPhoneWithRetry should log appropriate messages for FeignException and CallNotPermittedException"() {
        given: "an error response from updateDncPhoneWithRetry"
        def errorResponse = createErrorResponse("E001", "VALIDATION_ERR", "Validation Error")
        proxyMock.updateDncPhoneWithRetry(headers, productGroupId, updateRequest, rmId, ecId) >> errorResponse

        and: "getProductGroupId throws CallNotPermittedException"
        def circuitBreakerMessage = "Circuit breaker is OPEN and does not permit further calls"
        //def circuitBreakerException = CallNotPermittedException.create(circuitBreakerMessage)
        customerDataServiceFeignClient.getProductGroupId(headers, productGroupId) >> {
            CircuitBreakerTest.fail()
        }

        when: "callUpdateDncPhoneWithRetry is called"
        def result = service.callUpdateDncPhoneWithRetry(headers, productGroupId, updateRequest, rmId, ecId)

        then: "should handle the exception and log circuit breaker message"
        result != null

        and: "should create appropriate response structure"
        !result.body.isEmpty()
        result.body[0].dncListId == "17cd0894-3fa6-4d5d-b8e8-bd43b736daf3"

        !result.errors.isEmpty()
        def circuitBreakerError = result.errors.find { it.code == "9998" }
        circuitBreakerError != null
        //circuitBreakerError.message == "updateDncPhone failed,response from updatePhoneNumbers is null"
    }

    // Helper methods to create test responses
    private ResponseModel<UpdatePhoneNumbersResponse> createSuccessResponse() {
        def response = UpdatePhoneNumbersResponse.builder()
                .customAppError(null)
                .build()

        return new ResponseModel<UpdatePhoneNumbersResponse>(
            dataObj: response,
            status: new ResponseStatus("0000")
        )
    }
    
    private ResponseModel<UpdatePhoneNumbersResponse> createErrorResponse(String code, String status, String message) {
        def customAppError = UpdatePhoneNumbersResponse.CustomAppError.builder()
                .code(code)
                .status(status)
                .message(message)
                .httpStatus("400")
                .build()

        UpdatePhoneNumbersResponse response = UpdatePhoneNumbersResponse.builder()
                .customAppError(customAppError)
                .build()
        
        return new ResponseModel<UpdatePhoneNumbersResponse>(
            dataObj: response,
            status: new ResponseStatus("9004")
        )
    }

    private ResponseModel<UpdatePhoneNumbersResponse> createCustomAppErrorNullErrorResponse() {
        UpdatePhoneNumbersResponse response = UpdatePhoneNumbersResponse.builder()
                .customAppError(null)
                .build()

        return new ResponseModel<UpdatePhoneNumbersResponse>(
                dataObj: response,
                status: new ResponseStatus("9004")
        )
    }
}
