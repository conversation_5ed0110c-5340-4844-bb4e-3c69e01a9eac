package com.ttb.top.dncupdatestreamsservice.service


import com.ttb.top.dncupdatestreamsservice.service.implement.CustomerCacheServiceImpl
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.customerprofile.response.CustomerProfileResponse
import com.ttb.top.dncupdatestreamsservice.utils.ResourceReader
import spock.lang.Specification

class CustomerCacheServiceTest extends Specification {
    def customerCacheService = new CustomerCacheServiceImpl()
    def resourceReader = new ResourceReader()

    def "when call get customer then return data null"() {
        when:
        def actual = customerCacheService.getCustomer("001100000000000000000025552410")

        then:
        actual == null
    }

    def "when call set customer then return data success"() {
        given:
        def cacheCustomer =
                resourceReader.readValue(
                        "json/get_customer_mongo.json",
                        CustomerProfileResponse.class
                )

        when:
        def actual = customerCacheService.setCustomer("001100000000000000000025552410", cacheCustomer)

        then:
        actual != null
    }
}
