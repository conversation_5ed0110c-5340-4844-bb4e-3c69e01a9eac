package com.ttb.top.dncupdatestreamsservice.service

import com.mongodb.*
import com.ttb.top.dncupdatestreamsservice.entity.DncPhoneUpdateFailEntity
import com.ttb.top.dncupdatestreamsservice.model.mongo.dncupdatephonefail.DncPhoneUpdateFailTransaction
import com.ttb.top.dncupdatestreamsservice.model.streams.producer.DncUpdateFailProducer
import com.ttb.top.dncupdatestreamsservice.service.implement.DncPhoneUpdateFailTransactionInsertServiceImpl
import com.ttb.top.dncupdatestreamsservice.repository.DncPhoneUpdateTransactionRepository
import com.ttb.top.library.exceptionmodel.exception.GenericException
import org.bson.BsonInt32
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.http.HttpHeaders
import spock.lang.Specification
import com.ttb.top.dncupdatestreamsservice.utils.ResourceReader
import com.ttb.top.library.httpheaderhelper.constant.HttpHeaderConstant;
import com.ttb.top.dncupdatestreamsservice.constants.Constants;
import com.ttb.top.library.exceptionmodel.constant.ResponseCodeEnum;
import org.bson.BsonDocument
import org.springframework.data.mongodb.UncategorizedMongoDbException

import java.util.stream.Collectors;

class DncPhoneUpdateFailTransactionInsertServiceImplTest extends Specification {
    def headers = Mock(HttpHeaders)
    def dncPhoneUpdateTransactionRepository = Mock(DncPhoneUpdateTransactionRepository)
    def dncPhoneUpdateFailTransactionInsertService = new DncPhoneUpdateFailTransactionInsertServiceImpl(dncPhoneUpdateTransactionRepository)

    def "should save and update when found data and normal request"() {
        given:
        headers.add(HttpHeaderConstant.X_CORRELATION_ID, "testX-Cor");
        String rmId = "001100000000000000000025552410"
        String ecId = "4001532146"

        DncUpdateFailProducer.DncUpdatePhone bodyObject = DncUpdateFailProducer.DncUpdatePhone.builder()
                .dncListId("Test1")
                .dncList(new DncUpdateFailProducer.DncList())
                .build();
        List<DncUpdateFailProducer.DncUpdatePhone> deeBody = new ArrayList<>();
        deeBody.add(bodyObject);

        DncUpdateFailProducer.DncUpdateError dncUpdateFailProducer1 = DncUpdateFailProducer.DncUpdateError.builder()
                .httpStatus("TestHttpStatus1")
                .code("TestCode1")
                .message("TestMessage1")
                .status("TestStatus1")
                .build();

        DncUpdateFailProducer.DncUpdateError dncUpdateFailProducer2 = DncUpdateFailProducer.DncUpdateError.builder()
                .httpStatus("TestHttpStatus1")
                .code("TestCode1")
                .message("TestMessage1")
                .status("TestStatus1")
                .build();

        List<DncUpdateFailProducer.DncUpdateError> deeError = new ArrayList<>();
        deeError.add(dncUpdateFailProducer1);
        deeError.add(dncUpdateFailProducer2);

        DncUpdateFailProducer dncUpdateFailProducer = DncUpdateFailProducer.builder()
                .body(deeBody)
                .errors(deeError)
                .build();

        List<DncPhoneUpdateFailTransaction.Body> bodyList = dncUpdateFailProducer.getBody().stream()
                .map(body -> DncPhoneUpdateFailTransaction.Body.builder()
                        .dncListId(body.getDncListId())
                        .dncList(DncPhoneUpdateFailTransaction.DncList.builder()
                                .action(body.getDncList().getAction())
                                .phoneNumbers(body.getDncList().getPhoneNumbers())
                                .expirationDateTime(body.getDncList().getExpirationDateTime())
                                .build())
                        .build())
                .collect(Collectors.toList());

        List<DncPhoneUpdateFailTransaction.Error> errorList = dncUpdateFailProducer.getErrors().stream()
                .map(body -> DncPhoneUpdateFailTransaction.Error.builder()
                        .serviceName(Constants.POST_DNCLIST_PHONENUMBER_ENDPOINT)
                        .httpStatus(body.getHttpStatus())
                        .code(body.getCode())
                        .message(body.getMessage())
                        .status(body.getStatus())
                        .build())
                .collect(Collectors.toList());

        DncPhoneUpdateFailTransaction dncPhoneUpdateFailTransaction = DncPhoneUpdateFailTransaction.builder()
                .dncBody(bodyList)
                .dncError(errorList)
                .build();

        when:
        dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(headers,
                dncPhoneUpdateFailTransaction, rmId, ecId)

        then:
        1 * dncPhoneUpdateTransactionRepository.save({
            verifyAll(it, DncPhoneUpdateFailEntity) {
                rmId == "001100000000000000000025552410"
                ecId == "4001532146"
            }
        })
    }

    def "should throw Generic exception"() {
        given:
        headers.add(HttpHeaderConstant.X_CORRELATION_ID, "testX-Cor");

        String rmId = "001100000000000000000025552410"
        String ecId = "4001532146"

        DncUpdateFailProducer.DncUpdatePhone bodyObject = DncUpdateFailProducer.DncUpdatePhone.builder()
                .dncListId("Test1")
                .dncList(new DncUpdateFailProducer.DncList())
                .build();
        List<DncUpdateFailProducer.DncUpdatePhone> deeBody = new ArrayList<>();
        deeBody.add(bodyObject);

        DncUpdateFailProducer.DncUpdateError dncUpdateFailProducer1 = DncUpdateFailProducer.DncUpdateError.builder()
                .httpStatus("TestHttpStatus1")
                .code("TestCode1")
                .message("TestMessage1")
                .status("TestStatus1")
                .build();

        DncUpdateFailProducer.DncUpdateError dncUpdateFailProducer2 = DncUpdateFailProducer.DncUpdateError.builder()
                .httpStatus("TestHttpStatus1")
                .code("TestCode1")
                .message("TestMessage1")
                .status("TestStatus1")
                .build();

        List<DncUpdateFailProducer.DncUpdateError> deeError = new ArrayList<>();
        deeError.add(dncUpdateFailProducer1);
        deeError.add(dncUpdateFailProducer2);

        DncUpdateFailProducer dncUpdateFailProducer = DncUpdateFailProducer.builder()
                .body(deeBody)
                .errors(deeError)
                .build();

        List<DncPhoneUpdateFailTransaction.Body> bodyList = dncUpdateFailProducer.getBody().stream()
                .map(body -> DncPhoneUpdateFailTransaction.Body.builder()
                        .dncListId(body.getDncListId())
                        .dncList(DncPhoneUpdateFailTransaction.DncList.builder()
                                .action(body.getDncList().getAction())
                                .phoneNumbers(body.getDncList().getPhoneNumbers())
                                .expirationDateTime(body.getDncList().getExpirationDateTime())
                                .build())
                        .build())
                .collect(Collectors.toList());

        List<DncPhoneUpdateFailTransaction.Error> errorList = dncUpdateFailProducer.getErrors().stream()
                .map(body -> DncPhoneUpdateFailTransaction.Error.builder()
                        .serviceName(Constants.POST_DNCLIST_PHONENUMBER_ENDPOINT)
                        .httpStatus(body.getHttpStatus())
                        .code(body.getCode())
                        .message(body.getMessage())
                        .status(body.getStatus())
                        .build())
                .collect(Collectors.toList());

        DncPhoneUpdateFailTransaction dncPhoneUpdateFailTransaction = DncPhoneUpdateFailTransaction.builder()
                .dncBody(bodyList)
                .dncError(errorList)
                .build();

        dncPhoneUpdateTransactionRepository.save(_) >> { throw GenericException }

        when:
        dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(headers,
                dncPhoneUpdateFailTransaction, rmId, ecId)

        then:
        def e = thrown(GenericException)
        e.getErrorCode() == ResponseCodeEnum.GENERIC_ERROR_CODE.getCode()
    }

    def "should throw DataIntegrityViolationException"() {
        given:
        headers.add(HttpHeaderConstant.X_CORRELATION_ID, "testX-Cor");

        String rmId = "001100000000000000000025552410"
        String ecId = "4001532146"

        DncUpdateFailProducer.DncUpdatePhone bodyObject = DncUpdateFailProducer.DncUpdatePhone.builder()
                .dncListId("Test1")
                .dncList(new DncUpdateFailProducer.DncList())
                .build();
        List<DncUpdateFailProducer.DncUpdatePhone> deeBody = new ArrayList<>();
        deeBody.add(bodyObject);

        DncUpdateFailProducer.DncUpdateError dncUpdateFailProducer1 = DncUpdateFailProducer.DncUpdateError.builder()
                .httpStatus("TestHttpStatus1")
                .code("TestCode1")
                .message("TestMessage1")
                .status("TestStatus1")
                .build();

        DncUpdateFailProducer.DncUpdateError dncUpdateFailProducer2 = DncUpdateFailProducer.DncUpdateError.builder()
                .httpStatus("TestHttpStatus1")
                .code("TestCode1")
                .message("TestMessage1")
                .status("TestStatus1")
                .build();

        List<DncUpdateFailProducer.DncUpdateError> deeError = new ArrayList<>();
        deeError.add(dncUpdateFailProducer1);
        deeError.add(dncUpdateFailProducer2);

        DncUpdateFailProducer dncUpdateFailProducer = DncUpdateFailProducer.builder()
                .body(deeBody)
                .errors(deeError)
                .build();

        List<DncPhoneUpdateFailTransaction.Body> bodyList = dncUpdateFailProducer.getBody().stream()
                .map(body -> DncPhoneUpdateFailTransaction.Body.builder()
                        .dncListId(body.getDncListId())
                        .dncList(DncPhoneUpdateFailTransaction.DncList.builder()
                                .action(body.getDncList().getAction())
                                .phoneNumbers(body.getDncList().getPhoneNumbers())
                                .expirationDateTime(body.getDncList().getExpirationDateTime())
                                .build())
                        .build())
                .collect(Collectors.toList());

        List<DncPhoneUpdateFailTransaction.Error> errorList = dncUpdateFailProducer.getErrors().stream()
                .map(body -> DncPhoneUpdateFailTransaction.Error.builder()
                        .serviceName(Constants.POST_DNCLIST_PHONENUMBER_ENDPOINT)
                        .httpStatus(body.getHttpStatus())
                        .code(body.getCode())
                        .message(body.getMessage())
                        .status(body.getStatus())
                        .build())
                .collect(Collectors.toList());

        DncPhoneUpdateFailTransaction dncPhoneUpdateFailTransaction = DncPhoneUpdateFailTransaction.builder()
                .dncBody(bodyList)
                .dncError(errorList)
                .build();

        def writeError = new WriteError(121, "Document validation failure", new BsonDocument())
        def cause = new MongoWriteException(writeError, new ServerAddress())
        def dataIntegrityViolationException = new DataIntegrityViolationException("Schema violation", cause)

        dncPhoneUpdateTransactionRepository.save(_) >> { throw dataIntegrityViolationException }

        when:
        dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(headers,
                dncPhoneUpdateFailTransaction, rmId, ecId)

        then:
        def e = thrown(GenericException)
        e.getErrorCode() == ResponseCodeEnum.DATABASE_ERROR_CODE.getCode()
    }

    def "should throw UncategorizedMongoDbException"() {
        given:
        headers.add(HttpHeaderConstant.X_CORRELATION_ID, "testX-Cor");

        String rmId = "001100000000000000000025552410"
        String ecId = "4001532146"

        DncUpdateFailProducer.DncUpdatePhone bodyObject = DncUpdateFailProducer.DncUpdatePhone.builder()
                .dncListId("Test1")
                .dncList(new DncUpdateFailProducer.DncList())
                .build();
        List<DncUpdateFailProducer.DncUpdatePhone> deeBody = new ArrayList<>();
        deeBody.add(bodyObject);

        DncUpdateFailProducer.DncUpdateError dncUpdateFailProducer1 = DncUpdateFailProducer.DncUpdateError.builder()
                .httpStatus("TestHttpStatus1")
                .code("TestCode1")
                .message("TestMessage1")
                .status("TestStatus1")
                .build();

        DncUpdateFailProducer.DncUpdateError dncUpdateFailProducer2 = DncUpdateFailProducer.DncUpdateError.builder()
                .httpStatus("TestHttpStatus1")
                .code("TestCode1")
                .message("TestMessage1")
                .status("TestStatus1")
                .build();

        List<DncUpdateFailProducer.DncUpdateError> deeError = new ArrayList<>();
        deeError.add(dncUpdateFailProducer1);
        deeError.add(dncUpdateFailProducer2);

        DncUpdateFailProducer dncUpdateFailProducer = DncUpdateFailProducer.builder()
                .body(deeBody)
                .errors(deeError)
                .build();

        List<DncPhoneUpdateFailTransaction.Body> bodyList = dncUpdateFailProducer.getBody().stream()
                .map(body -> DncPhoneUpdateFailTransaction.Body.builder()
                        .dncListId(body.getDncListId())
                        .dncList(DncPhoneUpdateFailTransaction.DncList.builder()
                                .action(body.getDncList().getAction())
                                .phoneNumbers(body.getDncList().getPhoneNumbers())
                                .expirationDateTime(body.getDncList().getExpirationDateTime())
                                .build())
                        .build())
                .collect(Collectors.toList());

        List<DncPhoneUpdateFailTransaction.Error> errorList = dncUpdateFailProducer.getErrors().stream()
                .map(body -> DncPhoneUpdateFailTransaction.Error.builder()
                        .serviceName(Constants.POST_DNCLIST_PHONENUMBER_ENDPOINT)
                        .httpStatus(body.getHttpStatus())
                        .code(body.getCode())
                        .message(body.getMessage())
                        .status(body.getStatus())
                        .build())
                .collect(Collectors.toList());

        DncPhoneUpdateFailTransaction dncPhoneUpdateFailTransaction = DncPhoneUpdateFailTransaction.builder()
                .dncBody(bodyList)
                .dncError(errorList)
                .build();

        def errorDoc = new BsonDocument("code", new BsonInt32(112))
        def mongoCommandException = new MongoCommandException(errorDoc, new ServerAddress())
        def uncategorizedEx = new UncategorizedMongoDbException("Write conflict", mongoCommandException)

        dncPhoneUpdateTransactionRepository.save(_) >> { throw uncategorizedEx }

        when:
        dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(headers,
                dncPhoneUpdateFailTransaction, rmId, ecId)

        then:
        def e = thrown(GenericException)
        e.getErrorCode() == ResponseCodeEnum.DATABASE_ERROR_CODE.getCode()
    }

    def "should wrap MongoCommandException with wrong error code in GenericException"() {
        given:
        headers.add(HttpHeaderConstant.X_CORRELATION_ID, "testX-Cor");

        String rmId = "001100000000000000000025552410"
        String ecId = "4001532146"

        DncUpdateFailProducer.DncUpdatePhone bodyObject = DncUpdateFailProducer.DncUpdatePhone.builder()
                .dncListId("Test1")
                .dncList(new DncUpdateFailProducer.DncList())
                .build();
        List<DncUpdateFailProducer.DncUpdatePhone> deeBody = new ArrayList<>();
        deeBody.add(bodyObject);

        DncUpdateFailProducer.DncUpdateError dncUpdateFailProducer1 = DncUpdateFailProducer.DncUpdateError.builder()
                .httpStatus("TestHttpStatus1")
                .code("TestCode1")
                .message("TestMessage1")
                .status("TestStatus1")
                .build();

        DncUpdateFailProducer.DncUpdateError dncUpdateFailProducer2 = DncUpdateFailProducer.DncUpdateError.builder()
                .httpStatus("TestHttpStatus1")
                .code("TestCode1")
                .message("TestMessage1")
                .status("TestStatus1")
                .build();

        List<DncUpdateFailProducer.DncUpdateError> deeError = new ArrayList<>();
        deeError.add(dncUpdateFailProducer1);
        deeError.add(dncUpdateFailProducer2);

        DncUpdateFailProducer dncUpdateFailProducer = DncUpdateFailProducer.builder()
                .body(deeBody)
                .errors(deeError)
                .build();

        List<DncPhoneUpdateFailTransaction.Body> bodyList = dncUpdateFailProducer.getBody().stream()
                .map(body -> DncPhoneUpdateFailTransaction.Body.builder()
                        .dncListId(body.getDncListId())
                        .dncList(DncPhoneUpdateFailTransaction.DncList.builder()
                                .action(body.getDncList().getAction())
                                .phoneNumbers(body.getDncList().getPhoneNumbers())
                                .expirationDateTime(body.getDncList().getExpirationDateTime())
                                .build())
                        .build())
                .collect(Collectors.toList());

        List<DncPhoneUpdateFailTransaction.Error> errorList = dncUpdateFailProducer.getErrors().stream()
                .map(body -> DncPhoneUpdateFailTransaction.Error.builder()
                        .serviceName(Constants.POST_DNCLIST_PHONENUMBER_ENDPOINT)
                        .httpStatus(body.getHttpStatus())
                        .code(body.getCode())
                        .message(body.getMessage())
                        .status(body.getStatus())
                        .build())
                .collect(Collectors.toList());

        DncPhoneUpdateFailTransaction dncPhoneUpdateFailTransaction = DncPhoneUpdateFailTransaction.builder()
                .dncBody(bodyList)
                .dncError(errorList)
                .build();

        def commandEx = Mock(MongoCommandException) {
            getErrorCode() >> 999  // Not 112
        }
        def cause = new UncategorizedMongoDbException("conflict", commandEx)
        dncPhoneUpdateTransactionRepository.save(_ as DncPhoneUpdateFailEntity) >> { throw cause }

        when:
        dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(headers,
                dncPhoneUpdateFailTransaction, rmId, ecId)

        then:
        def ex = thrown(GenericException)
        ex.cause == cause
    }

    def "should wrap DataIntegrityViolationException with wrong cause in GenericException"() {
        given:
        headers.add(HttpHeaderConstant.X_CORRELATION_ID, "testX-Cor");

        String rmId = "001100000000000000000025552410"
        String ecId = "4001532146"

        DncUpdateFailProducer.DncUpdatePhone bodyObject = DncUpdateFailProducer.DncUpdatePhone.builder()
                .dncListId("Test1")
                .dncList(new DncUpdateFailProducer.DncList())
                .build();
        List<DncUpdateFailProducer.DncUpdatePhone> deeBody = new ArrayList<>();
        deeBody.add(bodyObject);

        DncUpdateFailProducer.DncUpdateError dncUpdateFailProducer1 = DncUpdateFailProducer.DncUpdateError.builder()
                .httpStatus("TestHttpStatus1")
                .code("TestCode1")
                .message("TestMessage1")
                .status("TestStatus1")
                .build();

        DncUpdateFailProducer.DncUpdateError dncUpdateFailProducer2 = DncUpdateFailProducer.DncUpdateError.builder()
                .httpStatus("TestHttpStatus1")
                .code("TestCode1")
                .message("TestMessage1")
                .status("TestStatus1")
                .build();

        List<DncUpdateFailProducer.DncUpdateError> deeError = new ArrayList<>();
        deeError.add(dncUpdateFailProducer1);
        deeError.add(dncUpdateFailProducer2);

        DncUpdateFailProducer dncUpdateFailProducer = DncUpdateFailProducer.builder()
                .body(deeBody)
                .errors(deeError)
                .build();

        List<DncPhoneUpdateFailTransaction.Body> bodyList = dncUpdateFailProducer.getBody().stream()
                .map(body -> DncPhoneUpdateFailTransaction.Body.builder()
                        .dncListId(body.getDncListId())
                        .dncList(DncPhoneUpdateFailTransaction.DncList.builder()
                                .action(body.getDncList().getAction())
                                .phoneNumbers(body.getDncList().getPhoneNumbers())
                                .expirationDateTime(body.getDncList().getExpirationDateTime())
                                .build())
                        .build())
                .collect(Collectors.toList());

        List<DncPhoneUpdateFailTransaction.Error> errorList = dncUpdateFailProducer.getErrors().stream()
                .map(body -> DncPhoneUpdateFailTransaction.Error.builder()
                        .serviceName(Constants.POST_DNCLIST_PHONENUMBER_ENDPOINT)
                        .httpStatus(body.getHttpStatus())
                        .code(body.getCode())
                        .message(body.getMessage())
                        .status(body.getStatus())
                        .build())
                .collect(Collectors.toList());

        DncPhoneUpdateFailTransaction dncPhoneUpdateFailTransaction = DncPhoneUpdateFailTransaction.builder()
                .dncBody(bodyList)
                .dncError(errorList)
                .build();

        def cause = new RuntimeException("not mongo write")
        def exception = new DataIntegrityViolationException("violation", cause)
        dncPhoneUpdateTransactionRepository.save(_ as DncPhoneUpdateFailEntity) >> { throw exception }

        when:
        dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(headers,
                dncPhoneUpdateFailTransaction, rmId, ecId)

        then:
        def ex = thrown(GenericException)
        ex.cause == exception
    }

    def "should not match if cause is not MongoCommandException"() {
        given:
        headers.add(HttpHeaderConstant.X_CORRELATION_ID, "testX-Cor");

        String rmId = "001100000000000000000025552410"
        String ecId = "4001532146"

        DncUpdateFailProducer.DncUpdatePhone bodyObject = DncUpdateFailProducer.DncUpdatePhone.builder()
                .dncListId("Test1")
                .dncList(new DncUpdateFailProducer.DncList())
                .build();
        List<DncUpdateFailProducer.DncUpdatePhone> deeBody = new ArrayList<>();
        deeBody.add(bodyObject);

        DncUpdateFailProducer.DncUpdateError dncUpdateFailProducer1 = DncUpdateFailProducer.DncUpdateError.builder()
                .httpStatus("TestHttpStatus1")
                .code("TestCode1")
                .message("TestMessage1")
                .status("TestStatus1")
                .build();

        DncUpdateFailProducer.DncUpdateError dncUpdateFailProducer2 = DncUpdateFailProducer.DncUpdateError.builder()
                .httpStatus("TestHttpStatus1")
                .code("TestCode1")
                .message("TestMessage1")
                .status("TestStatus1")
                .build();

        List<DncUpdateFailProducer.DncUpdateError> deeError = new ArrayList<>();
        deeError.add(dncUpdateFailProducer1);
        deeError.add(dncUpdateFailProducer2);

        DncUpdateFailProducer dncUpdateFailProducer = DncUpdateFailProducer.builder()
                .body(deeBody)
                .errors(deeError)
                .build();

        List<DncPhoneUpdateFailTransaction.Body> bodyList = dncUpdateFailProducer.getBody().stream()
                .map(body -> DncPhoneUpdateFailTransaction.Body.builder()
                        .dncListId(body.getDncListId())
                        .dncList(DncPhoneUpdateFailTransaction.DncList.builder()
                                .action(body.getDncList().getAction())
                                .phoneNumbers(body.getDncList().getPhoneNumbers())
                                .expirationDateTime(body.getDncList().getExpirationDateTime())
                                .build())
                        .build())
                .collect(Collectors.toList());

        List<DncPhoneUpdateFailTransaction.Error> errorList = dncUpdateFailProducer.getErrors().stream()
                .map(body -> DncPhoneUpdateFailTransaction.Error.builder()
                        .serviceName(Constants.POST_DNCLIST_PHONENUMBER_ENDPOINT)
                        .httpStatus(body.getHttpStatus())
                        .code(body.getCode())
                        .message(body.getMessage())
                        .status(body.getStatus())
                        .build())
                .collect(Collectors.toList());

        DncPhoneUpdateFailTransaction dncPhoneUpdateFailTransaction = DncPhoneUpdateFailTransaction.builder()
                .dncBody(bodyList)
                .dncError(errorList)
                .build();
        def nonCommandCause = new RuntimeException("not mongo command")
        def uncategorizedEx = new UncategorizedMongoDbException("mongo conflict", nonCommandCause)
        dncPhoneUpdateTransactionRepository.save(_ as DncPhoneUpdateFailEntity) >> { throw uncategorizedEx }

        when:
        dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(headers,
                dncPhoneUpdateFailTransaction, rmId, ecId)

        then:
        def ex = thrown(GenericException)
        ex.cause == uncategorizedEx
    }

}
