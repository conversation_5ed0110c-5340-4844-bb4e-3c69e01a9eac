package com.ttb.top.dncupdatestreamsservice.consumer

import com.ttb.top.dncupdatestreamsservice.service.implement.DncUpdateConsumerServiceImpl
import com.ttb.top.dncupdatestreamsservice.streams.consumer.EventHubConsumer
import org.springframework.kafka.listener.adapter.ConsumerRecordMetadata
import spock.lang.Specification

class EventHubConsumerTest extends Specification {

    def dncUpdateConsumerService = Mock(DncUpdateConsumerServiceImpl)
    def eventHubConsumer = new EventHubConsumer(dncUpdateConsumerService);

    def "should call processMessage when consume message from event hub"() {
        given:
        def message = "test"

        when:
        eventHubConsumer.listener(message, Mock(ConsumerRecordMetadata))

        then:
        1 * dncUpdateConsumerService.processMessage(message)
    }
}