# Logging level
logging.level.org.springframework.data.mongodb.core.MongoTemplate=DEBUG
logging.level.org.springframework.data.redis=DEBUG

# For integration test
spring.main.allow-bean-definition-overriding=true

# lookup-helper
translation-service.feign.client.url=http://translation-service:8080/

# lookup
lookup.type.name=top.response.status

# feign
feign.admin-service.name=admin-service
feign.admin-service.url=http://localhost:8080
feign.oneapp.customer-care-expose.name=oneapp-activity-service
feign.oneapp.customer-care-expose.url=http://xxxxx/
spring.cloud.openfeign.client.config.default.connectTimeout=60000
spring.cloud.openfeign.client.config.default.readTimeout=10000
feign.oneapp.customer-care-expose.header.api-key=

# decrypt-helper
db.encryption.aes.key=R+nddbXc87mAwUD9jvmY+f36pOoenInvEayGxqspuhE=@tZ76MfyqZbcPkNCB3WJ9ow==
decrypt.rsa.private.key=MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCyRrcxivnJosBTSmh4QgQplY9txAIH7FIigAhyiLCDLQtgtT5pD+11xW3RYnZwWioYQmGqkFMzigfKDOkDduvvZhA7m4+DW9ylxqGuKj644zRwzgiJ5U5qgRmHzmycj3dgiDs/7wP8BZhC48DzoMbbziCURkaFze8mUyrKsDYqqta0TYdDritVeL6Ud//D/CfZSZwOfMxy+BRNDJRADPt+msUFeu2ZBpRq8w/lPyV8vQ98fYSFqlV1N1lsiBOGEwY1ppqFZcnGXQ1K2VQvdC5jhHZyzLaor+XZt64X5OX8tsfjXaT83Th/yWuza1awzqveHZcxw7gyizTjosatlPvVAgMBAAECggEAJkumgo/2BGhfpASx2FNmDYDBJLUcMpODOUIDjobqU+NTNFz6oRr4yXm1k2rxQkU8EaYA0ODb3pBiB/cp/sKHABAOoJ9T/sW26i13AbC1dIXp9+lqUCTf6WT+FPw0vJTc8fGRuLQhSPvyrzu5cRwyW3k16mQGNiv8mWD4Kj4cBKH+V1bpUQaRClgp41ZOMVv+g6xeH9Trv41B5JT7Z2ycTvQVvSXSAtCebbfICH1ELbjfADwV8DH2wDbbrew5RxGSPiXBQcFYib9F3yoi80Zc+AsuYcQC0fNCsygkQ0wicMH8vVFKCRWL8mPQ6aUUXvslDws5JqDkUtdrQMpmpUUoaQKBgQDbzTsvNX4XVxJc36cxZtF4e5wMNNEmRXuvvukI7L/E5amBq6Rj0gRvP8KM5LnPzy4OE0mQVBpL92ppuoputYRsbFOV91AWgBAGGoFL8ho40wDLZ4HCU1Fj6Psl9O4+Rh28w8ZdmgWAvh7FGvvsNMaUL/9ewmLjhpp5f+FjYosNVwKBgQDPosjd92xNuxiDB3p5oXbFVrcqvcsBtZiWtfJftjl4SZQcKMjXqvTiY7Pqrz/SXl1ZVKKDC48cU8PrNSzBhcRwZ2IV1s+4rHbb8Dm8IWVyhTNuSn6ftDews/w5Xzgo/PazxzbXLnqq62LMdxXNjVDLAuN7NfzMmkURzFJv2KmYswKBgQCuC4aPzTW42ZOKwvYq4hV/57Ea4T+zpFVaRjtUe9Ml4A0mxnj3KbelN8GfuwV/DbiUIKWhiVcBTDqQ2cr/+u+OwwA0wY5DIsiNbLNxJZWp5Tq91YokC8Fo8XTdC2MTIIYvkH4kY+9zkBfhT4qn8OpFMPRvXlDbhRwQlTgtcDxXJQKBgBoiejf+IaKzDwXHFjJjEWkLXijCFOBVNCycIDLN4/PxBvR4abdDrGkmdYnvnw/ikstgrMfj15KQNJPRcJ23MZ+YU68+B41OH/PVC99TMMq2W1/hfoipjWzvaqrqAk6ecIr2Yz+4ePY0hI4J2zOxOt8isPFcPUKflFwGJMYxNj+jAoGAYblB6i9MIZu3bpk/WPFPHjHmtKN61pJ2fFrzXyDtk3d5S2+FkqywWCLNXkDD16b4yapOgbzVs0/yIUIO6250DihDpVvViddMAngYaQ7DWHUH5lbGRcPF8znc4XWijaYA0RaaLVhTKmr3NSXDxreOKgiNT4JdaFS7rlivFG4HXzU=
encrypt.rsa.public.key=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAska3MYr5yaLAU0poeEIEKZWPbcQCB+xSIoAIcoiwgy0LYLU+aQ/tdcVt0WJ2cFoqGEJhqpBTM4oHygzpA3br72YQO5uPg1vcpcahrio+uOM0cM4IieVOaoEZh85snI93YIg7P+8D/AWYQuPA86DG284glEZGhc3vJlMqyrA2KqrWtE2HQ64rVXi+lHf/w/wn2UmcDnzMcvgUTQyUQAz7fprFBXrtmQaUavMP5T8lfL0PfH2EhapVdTdZbIgThhMGNaaahWXJxl0NStlUL3QuY4R2csy2qK/l2beuF+Tl/LbH412k/N04f8lrs2tWsM6r3h2XMcO4Mos046LGrZT71QIDAQAB

# mongo
spring.data.mongodb.uri=mongodb://localhost:27017/topdevdb
spring.data.mongodb.username=app_user
spring.data.mongodb.password=app_password
spring.data.mongodb.dbname=topdevdb
spring.data.mongodb.authSource=admin

# redis config
spring.data.redis.host=localhost
spring.data.redis.port=6379
spring.data.redis.password=
spring.data.redis.ssl.enabled=false
adm.common.config.expire-at.time-of-day=06:30

# kafka config
spring.kafka.consumer=enable
azure.eventhub.password=demo-password
spring.kafka.consumer.bootstrap-servers=localhost:9092
spring.kafka.consumer.security.protocol=SASL_PLAINTEXT
spring.kafka.consumer.sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username="demo" password="${azure.eventhub.password}";
spring.kafka.consumer.sasl.mechanism=PLAIN
listen.auto.start=true
spring.kafka.listener.ack-mode=MANUAL_IMMEDIATE

kafka.activity-log.topic=activity-logging-local
kafka.activity-log.listener-container-id=activity-logging-listener
kafka.activity-log.group=activityLogStream-consumeActivityLog
