{"x_correlation_id": "2fc5e038-4011-4cdb-9d70-372396c3f0df", "ec_id": "001100000000000000000016547030", "rm_id": "001100000000000000000016547030", "modified_on": "2015-11-11T14:06:35.902+07:00", "created_on": "2015-11-11T14:06:35.902+07:00", "body": [{"dnc_list_id": "LN", "dnc_list": {"action": "Add", "phone_numbers": ["0812345679", "0812345678"], "expiration_date_time": "2024-03-18T17:30Z"}}, {"dnc_list_id": "INV", "dnc_list": {"action": "Add", "phone_numbers": ["0812345679", "0812345678"], "expiration_date_time": "2024-03-18T17:30Z"}}], "error": [{"service_name": "/v1/customer-data-service/customers/dncslist/{dncListId}/phonenumbers", "httpStatus": "404", "code": null, "message": null, "status": null}, {"service_name": "/v1/customer-data-service/customers/dncslist/{dncListId}/phonenumbers", "httpStatus": "400", "code": "invalid.date.value", "message": "2024-05-16T00:00 is invalid for expirationDateTime. It must be a UTC time in a format such as yyyy-MM-ddTHH:mmZ e.g. 2021-03-08T12:30Z", "status": "400"}]}