#Service Config Dev
spring.application.name=dnc-update-stream-service

# Feign client
spring.cloud.openfeign.okhttp.enabled=true

app.api.logging.max-length=5000
lookup.feignexception.enabled=true

# redis config
redis.enabled=true
spring.data.redis.host=localhost
spring.data.redis.port=6379
spring.data.redis.password=
spring.data.redis.ssl.enabled=false
cache.customer360.expire-seconds.web-customer-360-get-customer=1800

# decrypt-helper
db.encryption.aes.key=R+nddbXc87mAwUD9jvmY+f36pOoenInvEayGxqspuhE=@tZ76MfyqZbcPkNCB3WJ9ow==
decrypt.rsa.private.key=MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCyRrcxivnJosBTSmh4QgQplY9txAIH7FIigAhyiLCDLQtgtT5pD+11xW3RYnZwWioYQmGqkFMzigfKDOkDduvvZhA7m4+DW9ylxqGuKj644zRwzgiJ5U5qgRmHzmycj3dgiDs/7wP8BZhC48DzoMbbziCURkaFze8mUyrKsDYqqta0TYdDritVeL6Ud//D/CfZSZwOfMxy+BRNDJRADPt+msUFeu2ZBpRq8w/lPyV8vQ98fYSFqlV1N1lsiBOGEwY1ppqFZcnGXQ1K2VQvdC5jhHZyzLaor+XZt64X5OX8tsfjXaT83Th/yWuza1awzqveHZcxw7gyizTjosatlPvVAgMBAAECggEAJkumgo/2BGhfpASx2FNmDYDBJLUcMpODOUIDjobqU+NTNFz6oRr4yXm1k2rxQkU8EaYA0ODb3pBiB/cp/sKHABAOoJ9T/sW26i13AbC1dIXp9+lqUCTf6WT+FPw0vJTc8fGRuLQhSPvyrzu5cRwyW3k16mQGNiv8mWD4Kj4cBKH+V1bpUQaRClgp41ZOMVv+g6xeH9Trv41B5JT7Z2ycTvQVvSXSAtCebbfICH1ELbjfADwV8DH2wDbbrew5RxGSPiXBQcFYib9F3yoi80Zc+AsuYcQC0fNCsygkQ0wicMH8vVFKCRWL8mPQ6aUUXvslDws5JqDkUtdrQMpmpUUoaQKBgQDbzTsvNX4XVxJc36cxZtF4e5wMNNEmRXuvvukI7L/E5amBq6Rj0gRvP8KM5LnPzy4OE0mQVBpL92ppuoputYRsbFOV91AWgBAGGoFL8ho40wDLZ4HCU1Fj6Psl9O4+Rh28w8ZdmgWAvh7FGvvsNMaUL/9ewmLjhpp5f+FjYosNVwKBgQDPosjd92xNuxiDB3p5oXbFVrcqvcsBtZiWtfJftjl4SZQcKMjXqvTiY7Pqrz/SXl1ZVKKDC48cU8PrNSzBhcRwZ2IV1s+4rHbb8Dm8IWVyhTNuSn6ftDews/w5Xzgo/PazxzbXLnqq62LMdxXNjVDLAuN7NfzMmkURzFJv2KmYswKBgQCuC4aPzTW42ZOKwvYq4hV/57Ea4T+zpFVaRjtUe9Ml4A0mxnj3KbelN8GfuwV/DbiUIKWhiVcBTDqQ2cr/+u+OwwA0wY5DIsiNbLNxJZWp5Tq91YokC8Fo8XTdC2MTIIYvkH4kY+9zkBfhT4qn8OpFMPRvXlDbhRwQlTgtcDxXJQKBgBoiejf+IaKzDwXHFjJjEWkLXijCFOBVNCycIDLN4/PxBvR4abdDrGkmdYnvnw/ikstgrMfj15KQNJPRcJ23MZ+YU68+B41OH/PVC99TMMq2W1/hfoipjWzvaqrqAk6ecIr2Yz+4ePY0hI4J2zOxOt8isPFcPUKflFwGJMYxNj+jAoGAYblB6i9MIZu3bpk/WPFPHjHmtKN61pJ2fFrzXyDtk3d5S2+FkqywWCLNXkDD16b4yapOgbzVs0/yIUIO6250DihDpVvViddMAngYaQ7DWHUH5lbGRcPF8znc4XWijaYA0RaaLVhTKmr3NSXDxreOKgiNT4JdaFS7rlivFG4HXzU=
encrypt.rsa.public.key=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAska3MYr5yaLAU0poeEIEKZWPbcQCB+xSIoAIcoiwgy0LYLU+aQ/tdcVt0WJ2cFoqGEJhqpBTM4oHygzpA3br72YQO5uPg1vcpcahrio+uOM0cM4IieVOaoEZh85snI93YIg7P+8D/AWYQuPA86DG284glEZGhc3vJlMqyrA2KqrWtE2HQ64rVXi+lHf/w/wn2UmcDnzMcvgUTQyUQAz7fprFBXrtmQaUavMP5T8lfL0PfH2EhapVdTdZbIgThhMGNaaahWXJxl0NStlUL3QuY4R2csy2qK/l2beuF+Tl/LbH412k/N04f8lrs2tWsM6r3h2XMcO4Mos046LGrZT71QIDAQAB

# feign
feign.customer-data-service.name=customer-data-service
feign.customer-data-service.url=https://cmtop-dev1-services.tmbbank.local
feign.customer-data-service.customer-profile.get-customer.endpoint=/v1/customer-data-service/customer-profile/get-customer
feign.customer-data-service.customers-dnclist-phonenumbers.productgroupid.endpoint=/v1/customer-data-service/customers/dncslist/phonenumbers/{productGroupId}
feign.customer-data-service.customers-dnclist-productgroupid.endpoint=/v1/customer-data-service/customers/dncslist/{productGroupId}
feign.customer-data-service.custom-app.retry=3
feign.customer-data-service.custom-app.delay.ms=1000

feign.custom-app.dnclists-dncListId=ln:17cd0894-3fa6-4d5d-b8e8-bd43b736daf3|inv:d7ddebd7-207a-4f85-904c-24ae9195b666

#stream eventhub/kafka
# consumer
spring.kafka.consumer=enable
listen.auto.start=true
spring.kafka.consumer.bootstrap-servers=evh-a0551-top-dev.servicebus.windows.net:9093
spring.kafka.consumer.security.protocol=SASL_SSL
spring.kafka.consumer.sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="${azure.eventhub.password}";
spring.kafka.consumer.sasl.mechanism=PLAIN
eventhub.consumer.dnc-phone.update.topic=top-dev-dnc-phone-update

kafka.dnc-update-stream.listener-container-id=dnc-update-stream
kafka.dnc-update-stream.group-id=dnc-phone-update
#auto.offset.reset=latest
#enable.auto.commit=true
#auto.commit.interval.ms=1000

#publisher
spring.kafka.producer.bootstrap-servers=evh-a0551-top-dev.servicebus.windows.net:9093
spring.kafka.producer.security.protocol=SASL_SSL
spring.kafka.producer.sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="${azure.eventhub.password}";
spring.kafka.producer.sasl.mechanism=PLAIN
eventhub.producer.dnc-phone.update.topic=top-dev-dnc-phone-update
eventhub.producer.dnc-phone-update-fail.topic=top-dev-dnc-phone-update-fail
azure.eventhub.password=Endpoint=sb://evh-a0551-top-dev.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=58iSGMSgh2+NMQrEfTtBbtd2xyeZ1T3YC+AEhD51UAU=


# mongo
spring.data.mongodb.uri=mongodb+srv://topdncupdatestreamusr_dev:Pt0Pwd$<EMAIL>/topdev1db
spring.data.mongodb.username=topdncupdatestreamusr_dev
spring.data.mongodb.password=Pt0Pwd$dEv_24
spring.data.mongodb.dbname=topdevdb
spring.data.mongodb.authSource=admin
