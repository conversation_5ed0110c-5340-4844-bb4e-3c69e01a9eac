<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property name="LOGS" value="./logs" />
    <property resource="bootstrap.properties" />
    <appender name="CONSOLE_APPENDER" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <charset>UTF-8</charset>
            <pattern>%d{yyyy-MM-dd}T%d{HH:mm:ss.SSS}+07:00 ${spring.application.name} %replace(%t){'\s', ''} %level %logger{36} [%X{x-session-Id}] [%X{x-correlation-id}] - %m%n</pattern>
        </encoder>
    </appender>

    <root level="info">
        <appender-ref ref="CONSOLE_APPENDER"/>
    </root>
</configuration>
