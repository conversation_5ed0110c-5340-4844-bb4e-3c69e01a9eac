package com.ttb.top.dncupdatestreamsservice.feign.customerdata;

import com.ttb.top.dncupdatestreamsservice.configuration.CustomFeignConfig;
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.customerprofile.request.CustomerProfileRequest;
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.customerprofile.response.CustomerProfileResponse;
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.getproductgroupid.response.ProductGroupIdResponse;
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.updatephonenumbers.request.UpdatePhoneNumbersRequest;
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.updatephonenumbers.response.UpdatePhoneNumbersResponse;
import com.ttb.top.library.commonmodel.model.ResponseModel;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@CircuitBreaker(name = "customer-data-service")
@FeignClient(
        name = "${feign.customer-data-service.name}",
        url = "${feign.customer-data-service.url}",
        configuration = CustomFeignConfig.class)
public interface CustomerDataServiceFeignClient {

    @PostMapping("${feign.customer-data-service.customer-profile.get-customer.endpoint}")
    ResponseModel<CustomerProfileResponse> customerProfile(
            @RequestHeader HttpHeaders header,
            @Valid @RequestBody CustomerProfileRequest request
    );

    @PostMapping("${feign.customer-data-service.customers-dnclist-phonenumbers.productgroupid.endpoint}")
    ResponseModel<UpdatePhoneNumbersResponse> updatePhoneNumbers(
            @RequestHeader HttpHeaders header,
            @PathVariable String productGroupId,
            @Valid @RequestBody UpdatePhoneNumbersRequest request
    );

    @GetMapping("${feign.customer-data-service.customers-dnclist-productgroupid.endpoint}")
    ResponseModel<ProductGroupIdResponse> getProductGroupId(
            @RequestHeader HttpHeaders header,
            @Valid @PathVariable String productGroupId
    );

}
