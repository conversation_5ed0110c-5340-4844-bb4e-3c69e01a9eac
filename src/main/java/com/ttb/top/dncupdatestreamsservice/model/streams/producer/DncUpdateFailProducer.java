package com.ttb.top.dncupdatestreamsservice.model.streams.producer;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

@Getter
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class DncUpdateFailProducer {
    private List<DncUpdatePhone> body;
    private List<DncUpdateError> errors;

    @Getter
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    @ToString
    public static class DncUpdatePhone {
        private String dncListId;
        DncList dncList;
    }

    @Getter
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    @ToString
    public static class DncUpdateError {
        private String httpStatus;
        private String code;
        private String message;
        private String status;

    }

    @Getter
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    @ToString
    public static class DncList {
        private String action;
        private List<String> phoneNumbers;
        private String expirationDateTime;
    }
}
