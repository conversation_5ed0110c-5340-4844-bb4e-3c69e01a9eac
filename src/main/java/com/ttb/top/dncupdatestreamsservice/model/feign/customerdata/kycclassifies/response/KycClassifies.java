package com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.kycclassifies.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class KycClassifies {
    private String clType;
    private String clCode;
    private String clDesc1;
    private String clDesc2;
    private String clMisc1;
    private String clMisc2;
    private String clMisc3;
    private String clName;
}
