package com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.customerprofile.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdditionalKyc {
    private String idIssueCountry;
    private String cardNoExpire;
    private String swiftCode;
    private String fiCode;
    private String dopaFlag;
    private String ekycFlag;
    private int ial;
    private String amloRefuseFlag;
    private String partialFlag;
    private String limitedFlag;
    private String limitedDate;
    private String kycLastReviewDate;
    private String kycNextReviewDate;
    private String kycReviewLastMtnDate;
    private String kycReviewLastMtnUser;
    private String kycReviewLastMtnChannel;
    private String emailType;
    private String emailAddress;
    private String emailVerifyFlag;
    private String fatcaFlag;
    private String fatcaStatus;
    private String fatcaDate;
    private String createDate;
    private String createBy;
    private String updateDate;
    private String updateBy;
    private String politicalRelatedFlag;
    private String politicalRelatedDetail;
    private String customerStage;
    private String customerStageDate;
}
