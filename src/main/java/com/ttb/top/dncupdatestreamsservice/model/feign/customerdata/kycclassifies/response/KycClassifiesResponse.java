package com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.kycclassifies.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class KycClassifiesResponse {
    private String clType;
    private List<KycClassifies> kycClassifies;
}
