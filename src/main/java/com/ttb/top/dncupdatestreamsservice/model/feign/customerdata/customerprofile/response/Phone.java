package com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.customerprofile.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Phone {
    private String phoneType;
    private String phonePrefix;
    private String phoneNo;
    private String phoneNoFull;
    private String phoneNoTo;
    private String phoneNoExt;
    private String createBy;
    private String identPhone;
    private int phoneSeq;
    private String updateBy;
    private String createDate;
    private String updateDate;
}
