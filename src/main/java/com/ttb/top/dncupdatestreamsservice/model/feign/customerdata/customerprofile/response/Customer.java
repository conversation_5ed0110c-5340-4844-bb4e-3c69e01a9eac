package com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.customerprofile.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;

@Getter
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class Customer {
    private Profile profile;
    private Kyc kyc;
    private AdditionalKyc additionalKyc;
    private List<SourceOfIncome> sourceOfIncomes;
    private List<Consent> consents;
    private List<Address> addresses;
    private List<Phone> phones;
}
