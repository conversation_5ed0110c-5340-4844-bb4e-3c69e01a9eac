package com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.customerprofile.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class Profile {
    private String ccId;
    private String rmId;
    private String refId;
    private String migrationCategory;
    private String migrationInitialSrc;
    private String custTypeId;
    private String thaTname;
    private String engTname;
    private String thaFname;
    private String engFname;
    private String thaLname;
    private String engLname;
    private String idCard;
    private String registerId;
    private String taxId;
    private String birthDate;
    private String entryComp;
    private String entryBranch;
    private String entryBu;
    private String updateBranch;
    private String entryDate;
    private String entryBy;
    private String lastUpdate;
    private String updateBy;
}
