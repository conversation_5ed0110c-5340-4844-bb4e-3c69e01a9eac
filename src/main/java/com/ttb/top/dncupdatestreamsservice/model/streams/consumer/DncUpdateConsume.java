package com.ttb.top.dncupdatestreamsservice.model.streams.consumer;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;

@Getter
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class DncUpdateConsume {
    private Profile profile;
    private List<Body> body;

    @Getter
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Profile {
        private String ecId;
        private String rmId;

    }

    @Getter
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Body {
        private String productGroupId;
        private DncList dncList;

    }

    @Getter
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DncList {
        private String action;
        private List<String> phoneNumbers;
        private String expirationDateTime;
    }
}
