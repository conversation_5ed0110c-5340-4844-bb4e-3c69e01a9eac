package com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.updatephonenumbers.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class UpdatePhoneNumbersResponse {
    private CustomAppError customAppError;

    @Getter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ToString
    public static class CustomAppError {
        private String httpStatus;
        private String code;
        private String message;
        private String status;
    }

}
