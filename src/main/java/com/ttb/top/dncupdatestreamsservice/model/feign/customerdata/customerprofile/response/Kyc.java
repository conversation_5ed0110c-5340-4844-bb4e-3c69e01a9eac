package com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.customerprofile.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Kyc {
    private String rmId;
    private String involvedpartytype;
    private String customerType;
    private String idType;
    private String idNo;
    private String idReleasedDate;
    private String idExpireDate;
    private String idBirthDate;
    private String nationality;
    private String nationality2;
    private String gender;
    private String maritalStatus;
    private int noOfChild;
    private String titleCode;
    private String firstNameThai;
    private String middleNameThai;
    private String lastNameThai;
    private String firstNameEng;
    private String middleNameEng;
    private String lastNameEng;
    private String occupationCode;
    private String businessTypeDesc;
    private String startWorkDate;
    private int totalWorkYear;
    private String educationCode;
    private String businessTypeCode;
    private String businessTypeCode2;
    private String businessTypeCode3;
    private String customerLevel;
    private String customerStatus;
    private String legalStatus;
    private String behaviorFlag;
    private String behaviorReason;
    private String swfRiskResult;
    private int salary;
    private int otherIncome;
    private String countryOfIncome;
    private int salesVolumeAmt;
    private int noOfEmployee;
    private String taxId;
    private String additionalTitle;
    private String createDate;
    private String createBy;
    private String lastUpdate;
    private String updateBy;
    private String systemOwner;
    private String systemUpdate;
    private String fullFillFlag;
    private String workingPlace;
    private String employmentStatus;
    private String employmentCharacteristic;
}
