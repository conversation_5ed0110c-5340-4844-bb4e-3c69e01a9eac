package com.ttb.top.dncupdatestreamsservice.model.mongo.dncupdatephonefail;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;

@Getter
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DncPhoneUpdateFailTransaction {

    public static final String DNC_ID = "_id";
    public static final String X_CORRELATION_ID = "x_correlation_id";
    public static final String EC_ID = "ec_id";
    public static final String RM_ID = "rm_id";
    public static final String MODIFIED_ON = "modified_on";
    public static final String CREATED_ON = "created_on";
    public static final String BODY = "body";
    public static final String DNC_LIST_ID = "dnc_list_id";
    public static final String DNC_LIST = "dnc_list";
    public static final String ACTION = "action";
    public static final String PHONE_NUMBERS = "phone_numbers";
    public static final String EXPIRATION_DATE_TIME = "expiration_date_time";
    public static final String ERROR = "error";
    public static final String SERVICE_NAME = "service_name";
    public static final String HTTP_STATUS = "http_status";
    public static final String CODE = "code";
    public static final String MESSAGE = "message";
    public static final String STATUS = "status";

    @JsonProperty(DNC_ID)
    private String id;
    @JsonProperty(X_CORRELATION_ID)
    private String correlationId;
    @JsonProperty(RM_ID)
    private String rmId;
    @JsonProperty(EC_ID)
    private String ecId;
    @JsonProperty(MODIFIED_ON)
    private String modifiedOn;
    @JsonProperty(CREATED_ON)
    private String createdOn;
    @JsonProperty(BODY)
    private List<Body> dncBody;
    @JsonProperty(ERROR)
    private List<Error> dncError;

    @Getter
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Body {

        @JsonProperty(DNC_LIST_ID)
        private String dncListId;
        @JsonProperty(DNC_LIST)
        private DncList dncList;

    }

    @Getter
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DncList {

        @JsonProperty(ACTION)
        private String action;
        @JsonProperty(PHONE_NUMBERS)
        private List<String> phoneNumbers;
        @JsonProperty(EXPIRATION_DATE_TIME)
        private String expirationDateTime;

    }

    @Getter
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Error {

        @JsonProperty(SERVICE_NAME)
        private String serviceName;
        @JsonProperty(HTTP_STATUS)
        private String httpStatus;
        @JsonProperty(CODE)
        private String code;
        @JsonProperty(MESSAGE)
        private String message;
        @JsonProperty(STATUS)
        private String status;

    }
}
