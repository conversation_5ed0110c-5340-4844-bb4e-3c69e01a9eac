package com.ttb.top.dncupdatestreamsservice.model.streams.producer;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DncUpdateFail {
    private List<DncBody> body;
    private List<DncError> error;

    @Getter
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DncBody {
        private String dncListId;
        private DncList dncList;
    }

    @Getter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DncList {
        private String action;
        private List<String> phoneNumbers;
        private String expirationDateTime;
    }

    @Getter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DncError {
        private String serviceName;
        private String httpStatus;
        private String code;
        private String message;
        private String status;
    }
}


