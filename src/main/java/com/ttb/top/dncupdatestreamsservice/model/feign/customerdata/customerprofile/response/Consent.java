package com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.customerprofile.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Consent {
    private String typeOfConsent;
    private String flag;
    private String versionNumber;
    private String channel;
    private String userId;
    private LocalDate signupDate;
    private LocalDateTime systemDate;
}
