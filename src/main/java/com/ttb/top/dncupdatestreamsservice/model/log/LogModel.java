package com.ttb.top.dncupdatestreamsservice.model.log;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Value;

import java.time.Instant;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LogModel {
    private String timeStamp;
    private String level;
    @Value("${spring.application.name}")
    private String serviceName;
    private String correlationId;
    private String httpStatusCode;
    private String action;
    private String url;
    private String requestMessage;
    private String responseMessage;

    @Override
    public String toString() {

        this.timeStamp = DateTimeFormatter
                .ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'")
                .withZone(ZoneOffset.UTC)
                .format(Instant.now().truncatedTo(ChronoUnit.SECONDS));
        return new StringBuilder()
                .append(this.timeStamp + " ")
                .append(level + " ")
                .append(this.serviceName + " ")
                .append("[" + correlationId + "] ")
                .append("- HTTP Status:" + httpStatusCode + " ")
                .append("- " + action + " ")
                .append("- " + url + " ")
                .append("\n - Request Message - " + requestMessage + " ")
                .append("\n - Response Message - " + responseMessage + " ")
                .toString();
    }
}

