package com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.customerprofile.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Address {
    private String identAddressPri;
    private String identAddressReg;
    private String identAddressOff;
    private String identEmployment;
    private int addrSeq;
    private String regisAddrFlag;
    private String currAddrFlag;
    private String workAddrFlag;
    private String hoAddrFlag;
    private String workingPlace;
    private String addressNo;
    private String buildVillageName;
    private String roomNo;
    private String floor;
    private String moo;
    private String soi;
    private String road;
    private String subDistrict;
    private String district;
    private String province;
    private String postalCode;
    private String accomOwnerType;
    private String country;
    private String provinceOther;
    private String returnMail;
    private String returnDate;
    private String systemCreate;
    private String systemUpdate;
    private String createDate;
    private String createBy;
    private String updateDate;
    private String updateBy;
}
