package com.ttb.top.dncupdatestreamsservice.mapper;

import com.ttb.top.dncupdatestreamsservice.entity.DncPhoneUpdateFailEntity;
import com.ttb.top.dncupdatestreamsservice.model.mongo.dncupdatephonefail.DncPhoneUpdateFailTransaction;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface DncPhoneUpdateFailMapper {

    DncPhoneUpdateFailMapper INSTANCE = Mappers.getMapper(DncPhoneUpdateFailMapper.class);

    DncPhoneUpdateFailEntity mapDncPhoneUpdateFailToEntity(DncPhoneUpdateFailTransaction phoneUpdateFailTransaction);

}
