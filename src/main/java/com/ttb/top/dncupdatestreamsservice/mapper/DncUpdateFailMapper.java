package com.ttb.top.dncupdatestreamsservice.mapper;

import com.ttb.top.dncupdatestreamsservice.model.mongo.dncupdatephonefail.DncPhoneUpdateFailTransaction;
import com.ttb.top.dncupdatestreamsservice.model.streams.producer.DncUpdateFail;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;
import static org.mapstruct.MappingConstants.ComponentModel.SPRING;

@Mapper(componentModel = SPRING)
public interface DncUpdateFailMapper {

    DncUpdateFailMapper INSTANCE = Mappers.getMapper(DncUpdateFailMapper.class);

    DncUpdateFail.DncBody mapDncUpdatePhoneToDncBody(DncPhoneUpdateFailTransaction.Body dncUpdatePhone);

    DncUpdateFail.DncError mapDncUpdateErrorToDncError(DncPhoneUpdateFailTransaction.Error dncUpdateError);

    List<DncUpdateFail.DncBody> mapDncUpdatePhoneListToDncBodyList(List<DncPhoneUpdateFailTransaction.Body>
                                                                           dncUpdatePhone);

    List<DncUpdateFail.DncError> mapDncUpdateErrorListToDncErrorList(List<DncPhoneUpdateFailTransaction.Error>
                                                                             dncUpdateError);
}
