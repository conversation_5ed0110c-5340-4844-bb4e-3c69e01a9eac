package com.ttb.top.dncupdatestreamsservice.configuration;

import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class EventHubConsumerConfig {
    private static final String SASL_SSL_PROTOCOL = "SASL_SSL";

    @Value("${azure.eventhub.password}")
    private String eventHubPassword;
    @Value("${spring.kafka.consumer.bootstrap-servers}")
    private String consumerBootstrapAddress;
    @Value("${spring.kafka.consumer.security.protocol}")
    private String consumerProtocol;
    @Value("${spring.kafka.consumer.sasl.jaas.config}")
    private String consumerJaasConfig;
    @Value("${spring.kafka.consumer.sasl.mechanism}")
    private String consumerMechanism;
    //    @Value("${auto.offset.reset}")
    //    private String offsetReset;
    //    @Value("${enable.auto.commit}")
    //    private String enableAutoCommit;
    //    @Value("${auto.commit.interval.ms}")
    //    private String autoCommitInterval;


    @Bean
    @ConditionalOnProperty(
        value = {"spring.kafka.consumer"},
        havingValue = "enable"
    )
    public ConsumerFactory<String, String> streamHelperConsumerFactory() {
        Map<String, Object> configProps = new HashMap<>();
        configProps.put("bootstrap.servers", this.consumerBootstrapAddress);
        configProps.put("key.deserializer", StringDeserializer.class);
        configProps.put("value.deserializer", StringDeserializer.class);
        //        configProps.put("auto.offset.reset", offsetReset);
        //        configProps.put("enable.auto.commit", enableAutoCommit);
        //        configProps.put("auto.commit.interval.ms", autoCommitInterval);
        if (SASL_SSL_PROTOCOL.equals(this.consumerProtocol)) {
            configProps.put("security.protocol", SASL_SSL_PROTOCOL);
            configProps.put("sasl.jaas.config",
                this.consumerJaasConfig.replace("${azure.eventhub.password}", this.eventHubPassword));
            configProps.put("sasl.mechanism", this.consumerMechanism);
        }
        return new DefaultKafkaConsumerFactory<>(configProps);
    }

    @Bean
    @ConditionalOnProperty(
        value = {"spring.kafka.consumer"},
        havingValue = "enable"
    )
    @ConditionalOnBean({ConsumerFactory.class})
    public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, String>>
        kafkaListenerContainerFactory() {

        ConcurrentKafkaListenerContainerFactory<String, String> factory =
            new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(this.streamHelperConsumerFactory());
        factory.getContainerProperties().setStopImmediate(true);
        return factory;
    }
}
