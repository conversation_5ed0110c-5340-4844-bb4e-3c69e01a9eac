package com.ttb.top.dncupdatestreamsservice.utils;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.ttb.top.dncupdatestreamsservice.model.log.LogModel;
import lombok.SneakyThrows;

public class AppLog {
    private static final ObjectMapper objectMapper = JsonMapper.builder()
            .addModule(new JavaTimeModule())
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .build();

    @SneakyThrows
    public static String getStringLog(String level, String correlationId, String statusCode, String action, String url,
                                Object request, Object response) {
        return LogModel.builder()
                .correlationId(correlationId)
                .httpStatusCode(statusCode)
                .action(action)
                .url(url)
                .requestMessage(objectMapper.writeValueAsString(request))
                .responseMessage(objectMapper.writeValueAsString(response))
                .level(level).build().toString();
    }
}
