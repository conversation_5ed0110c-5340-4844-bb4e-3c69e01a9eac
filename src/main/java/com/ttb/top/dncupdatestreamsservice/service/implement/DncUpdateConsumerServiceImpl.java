package com.ttb.top.dncupdatestreamsservice.service.implement;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ttb.top.dncupdatestreamsservice.constants.Constants;
import com.ttb.top.dncupdatestreamsservice.feign.customerdata.CustomerDataServiceFeignClient;
import com.ttb.top.dncupdatestreamsservice.mapper.DncUpdateFailMapper;
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.customerprofile.request.CustomerProfileRequest;
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.customerprofile.response.CustomerFeignExceptionResponse;
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.customerprofile.response.CustomerProfileResponse;
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.customerprofile.response.Phone;
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.updatephonenumbers.request.UpdatePhoneNumbersRequest;
import com.ttb.top.dncupdatestreamsservice.model.mongo.dncupdatephonefail.DncPhoneUpdateFailTransaction;
import com.ttb.top.dncupdatestreamsservice.model.streams.consumer.DncUpdateConsume;
import com.ttb.top.dncupdatestreamsservice.model.streams.producer.DncUpdateFail;
import com.ttb.top.dncupdatestreamsservice.model.streams.producer.DncUpdateFailProducer;
import com.ttb.top.dncupdatestreamsservice.service.CustomerCacheService;
import com.ttb.top.dncupdatestreamsservice.service.DncPhoneUpdateFailTransactionInsertService;
import com.ttb.top.dncupdatestreamsservice.service.DncUpdateConsumerService;
import com.ttb.top.dncupdatestreamsservice.service.RetryUpdateDncPhoneService;
import com.ttb.top.dncupdatestreamsservice.streams.producer.EventHubProducer;
import com.ttb.top.dncupdatestreamsservice.utils.AppLog;
import com.ttb.top.library.commonmodel.model.ResponseModel;
import com.ttb.top.library.exceptionmodel.exception.GenericException;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
@Service
@RequiredArgsConstructor
public class DncUpdateConsumerServiceImpl implements DncUpdateConsumerService {
    private final ObjectMapper objectMapper;
    private final CustomerDataServiceFeignClient customerDataServiceFeignClient;
    private final CustomerCacheService customerCacheService;
    private final DncPhoneUpdateFailTransactionInsertService dncPhoneUpdateFailTransactionInsertService;
    private final RetryUpdateDncPhoneService retryUpdateDncPhoneService;
    private final EventHubProducer<DncUpdateFail> producer;
    private final DncUpdateFailMapper dncUpdateFailMapper;
    CustomerProfileResponse customerProfileResponse = null;

    @Value("${eventhub.producer.dnc-phone-update-fail.topic}")
    private String topicName;

    @Override
    public void processMessage(String message) {

        DncUpdateConsume dncUpdateConsume = null;
        HttpHeaders headers = null;
        DncPhoneUpdateFailTransaction dncPhoneUpdateFailTransaction = null;
        try {
            HashMap<String, String> data = objectMapper.readValue(message,
                    new TypeReference<HashMap<String, String>>() {});

            headers = objectMapper.readValue(data.get("headers"), HttpHeaders.class);
            dncUpdateConsume = objectMapper.readValue(data.get("dncUpdateConsumer"),
                    DncUpdateConsume.class);

            String crmId = dncUpdateConsume.getProfile().getRmId() == null
                    ? dncUpdateConsume.getProfile().getEcId() : dncUpdateConsume.getProfile().getRmId();

            // get customer profile from cache redis
            customerProfileResponse = customerCacheService.getCustomer(crmId);

            if (customerProfileResponse == null) {

                CustomerProfileRequest customerProfileRequest = dncUpdateConsume.getProfile().getEcId() != null
                        ? CustomerProfileRequest.builder().ecId(dncUpdateConsume.getProfile().getEcId()).build() :
                          CustomerProfileRequest.builder().rmId(dncUpdateConsume.getProfile().getRmId()).build();

                ResponseModel<CustomerProfileResponse> customerProfile =
                        customerDataServiceFeignClient.customerProfile(headers, customerProfileRequest);

                customerProfileResponse = customerProfile.getDataObj();
                customerCacheService.setCustomer(crmId, customerProfileResponse);
            }

            // update DNC mobile number process
            // map phone number

            List<DncUpdateFailProducer> dncUpdateFailList = new ArrayList<>();

            HttpHeaders finalHeaders = headers;
            dncUpdateConsume.getBody().forEach(item -> {
                UpdatePhoneNumbersRequest updatePhoneNumbersRequest = null;
                if ((item.getDncList().getExpirationDateTime() == null)
                        || item.getDncList().getExpirationDateTime()
                        .equalsIgnoreCase("4000-12-31T23:59Z")) {
                    updatePhoneNumbersRequest = UpdatePhoneNumbersRequest.builder()
                        .action(item.getDncList().getAction())
                        .phoneNumbers(Arrays.stream(listPhoneNumber(customerProfileResponse)).toList())
                        .build();
                } else {
                    updatePhoneNumbersRequest = UpdatePhoneNumbersRequest.builder()
                        .action(item.getDncList().getAction())
                        .phoneNumbers(Arrays.stream(listPhoneNumber(customerProfileResponse)).toList())
                        .expirationDateTime(item.getDncList().getExpirationDateTime())
                        .build();
                }

                //POST v1/customer-data-service/customers/dnclist/phonenumbers/{productgroupId}
                DncUpdateFailProducer dncUpdateFailProducer = retryUpdateDncPhoneService.callUpdateDncPhoneWithRetry(
                        finalHeaders, item.getProductGroupId(), updatePhoneNumbersRequest,
                        customerProfileResponse.getCustomer().getProfile().getRmId(),
                        customerProfileResponse.getCustomer().getProfile().getCcId());

                dncUpdateFailList.add(dncUpdateFailProducer);
            });

            DncUpdateFailProducer dncUpdateFailProducer = DncUpdateFailProducer.builder()
                    .body(new ArrayList<DncUpdateFailProducer.DncUpdatePhone>())
                    .errors(new ArrayList<DncUpdateFailProducer.DncUpdateError>())
                    .build();

            AtomicReference<Boolean> isFeignFail = new AtomicReference<>(false);

            dncUpdateFailList.forEach(item -> {
                dncUpdateFailProducer.getBody().addAll(item.getBody());
                dncUpdateFailProducer.getErrors().addAll(item.getErrors());

                if ((!item.getErrors().isEmpty() && !item.getBody().isEmpty())
                        && (item.getBody().size() <= 2 && item.getErrors().size() <= 2)) {
                    isFeignFail.set(true);
                }
            });

            log.info("dncUpdateFailProducer: {}", dncUpdateFailProducer);
            checkFeignFail(isFeignFail, dncUpdateFailProducer, headers, dncUpdateConsume, dncUpdateFailList);

        } catch (FeignException ex) {
            log.error("FeignException: {}", ex.getMessage(), ex);
            dncPhoneUpdateFailTransaction = handleFeignException(ex, dncUpdateConsume);
        } catch (JsonProcessingException ex) {
            log.error("JsonProcessingException: {}", ex.getMessage(), ex);
            throw new GenericException(ex.getMessage());
        } catch (Exception ex) {
            log.error("Exception: {}", ex.getMessage(), ex);
            dncPhoneUpdateFailTransaction = handleException(ex, dncUpdateConsume);
        }

        if (dncPhoneUpdateFailTransaction != null) {
            dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(
                    headers,
                    dncPhoneUpdateFailTransaction,
                    dncUpdateConsume.getProfile().getRmId(),
                    dncUpdateConsume.getProfile().getEcId());

            // publish to eventhub
            DncUpdateFail dncUpdateFailFromPost = DncUpdateFail.builder()
                    .body(dncUpdateFailMapper.mapDncUpdatePhoneListToDncBodyList(
                            dncPhoneUpdateFailTransaction.getDncBody()))
                    .error(dncUpdateFailMapper.mapDncUpdateErrorListToDncErrorList(
                            dncPhoneUpdateFailTransaction.getDncError()))
                    .build();

            boolean isSuccess = producer.send(topicName, dncUpdateFailFromPost);

            applicationLog(headers.getFirst("x-correlation-id"),
                    "Success",
                    "Published",
                    "publish to eventhub topic : " + topicName,
                    dncUpdateFailFromPost,
                    isSuccess);
        }
    }

    private  DncPhoneUpdateFailTransaction handleException(Exception ex, DncUpdateConsume dncUpdateConsume) {
        DncPhoneUpdateFailTransaction dncPhoneUpdateFailTransaction = null;

        List<DncPhoneUpdateFailTransaction.Body> bodyList = dncUpdateConsume.getBody().stream()
                .map(body -> DncPhoneUpdateFailTransaction.Body.builder()
                        .dncListId(body.getProductGroupId())
                        .dncList(DncPhoneUpdateFailTransaction.DncList.builder()
                                .action(body.getDncList().getAction())
                                .phoneNumbers(null)
                                .expirationDateTime(body.getDncList().getExpirationDateTime())
                                .build())
                        .build())
                .toList();


        DncPhoneUpdateFailTransaction.Error errorObject = DncPhoneUpdateFailTransaction.Error.builder()
                .serviceName(Constants.POST_DNCLIST_PHONENUMBER_ENDPOINT)
                .httpStatus("")
                .code("")
                .message(ex.getMessage())
                .status("")
                .build();
        List<DncPhoneUpdateFailTransaction.Error> errorList = new ArrayList<>();
        errorList.add(errorObject);

        return DncPhoneUpdateFailTransaction.builder().dncBody(bodyList)
                .dncError(errorList)
                .build();
    }

    private DncPhoneUpdateFailTransaction handleFeignException(FeignException ex, DncUpdateConsume dncUpdateConsume) {
        DncPhoneUpdateFailTransaction dncPhoneUpdateFailTransaction = null;
        try {
            CustomerFeignExceptionResponse errorMap = objectMapper.readValue(ex.contentUTF8(),
                    CustomerFeignExceptionResponse.class);

            List<DncPhoneUpdateFailTransaction.Body> bodyList = dncUpdateConsume.getBody().stream()
                    .map(body -> DncPhoneUpdateFailTransaction.Body.builder()
                            .dncListId(body.getProductGroupId())
                            .dncList(DncPhoneUpdateFailTransaction.DncList.builder()
                                    .action(body.getDncList().getAction())
                                    .phoneNumbers(null)
                                    .expirationDateTime(body.getDncList().getExpirationDateTime())
                                    .build())
                            .build())
                    .toList();

            DncPhoneUpdateFailTransaction.Error errorObject = DncPhoneUpdateFailTransaction.Error.builder()
                    .serviceName(Constants.GET_CUSTOMER_PROFILE_ENDPOINT)
                    .httpStatus(String.valueOf(ex.status()))
                    .code(errorMap.getStatus().getHeader())
                    .message(errorMap.getStatus().getDescription())
                    .status(errorMap.getStatus().getCode())
                    .build();
            List<DncPhoneUpdateFailTransaction.Error> errorList = new ArrayList<>();
            errorList.add(errorObject);

            dncPhoneUpdateFailTransaction = DncPhoneUpdateFailTransaction.builder().dncBody(bodyList)
                                                                                   .dncError(errorList)
                                                                                   .build();
        } catch (Exception e) {
            log.error("Exception: {}", e.getMessage(), e);
        }

        return dncPhoneUpdateFailTransaction;
    }


    private String[]  listPhoneNumber(CustomerProfileResponse customerProfileResponse) {
        return customerProfileResponse.getCustomer().getPhones().stream()
                .filter(phone -> phone != null && Arrays.asList(new String[]{"M", "B", "R"})
                        .contains(phone.getPhoneType()))
                .map(Phone::getPhoneNoFull)
                .filter(Objects::nonNull)
                .toArray(String[]::new);
    }

    private void checkFeignFail(AtomicReference<Boolean> isFeignFail, DncUpdateFailProducer dncUpdateFailProducer,
                                HttpHeaders headers, DncUpdateConsume dncUpdateConsume,
                                List<DncUpdateFailProducer> dncUpdateFailList) throws JsonProcessingException {
        if (Boolean.TRUE.equals(isFeignFail.get())) {

            //TEP Service processing error
            List<DncPhoneUpdateFailTransaction.Body> bodyList = dncUpdateFailProducer.getBody().stream()
                    .map(body -> DncPhoneUpdateFailTransaction.Body.builder()
                            .dncListId(body.getDncListId())
                            .dncList(DncPhoneUpdateFailTransaction.DncList.builder()
                                    .action(body.getDncList().getAction())
                                    .phoneNumbers(body.getDncList().getPhoneNumbers())
                                    .expirationDateTime(body.getDncList().getExpirationDateTime())
                                    .build())
                            .build())
                    .toList();

            AtomicInteger index = new AtomicInteger();
            List<DncPhoneUpdateFailTransaction.Error> errorList = dncUpdateFailProducer.getErrors().stream()
                    .map(body -> DncPhoneUpdateFailTransaction.Error.builder()
                            .serviceName(Constants.POST_DNCLIST_PHONENUMBER_ENDPOINT
                                    .concat(bodyList.get(bodyList.size() == 1 ? 0 :
                                            index.getAndIncrement()).getDncListId()))
                            .httpStatus(body.getHttpStatus())
                            .code(body.getCode())
                            .message(body.getMessage())
                            .status(body.getStatus())
                            .build())
                    .toList();

            DncPhoneUpdateFailTransaction dncPhoneUpdateFailTransaction = DncPhoneUpdateFailTransaction.builder()
                    .dncBody(bodyList)
                    .dncError(errorList)
                    .build();

            // check if error code is 9004 then publish to eventhub
            for (DncUpdateFailProducer.DncUpdateError error : dncUpdateFailProducer.getErrors()) {
                if (!"0000".equalsIgnoreCase(error.getCode())) {
                    // publish to eventhub
                    DncUpdateFail dncUpdateFailFromPost = DncUpdateFail.builder()
                            .body(dncUpdateFailMapper.mapDncUpdatePhoneListToDncBodyList(
                                    dncPhoneUpdateFailTransaction.getDncBody()))
                            .error(dncUpdateFailMapper.mapDncUpdateErrorListToDncErrorList(
                                    dncPhoneUpdateFailTransaction.getDncError()))
                            .build();
                    boolean isSuccess = producer.send(topicName, dncUpdateFailFromPost);

                    // log to app log
                    applicationLog(headers.getFirst("x-correlation-id"),
                            "Success",
                            "Published",
                            "publish to eventhub topic : " + topicName,
                            dncUpdateFailFromPost,
                            isSuccess);
                    break;
                }
            }

            // insert to mongo
            dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(
                    headers,
                    dncPhoneUpdateFailTransaction,
                    dncUpdateConsume.getProfile().getRmId(),
                    dncUpdateConsume.getProfile().getEcId());
        }
    }

    private void applicationLog(String xcorrelation, String httpStatusCode, String action,
                                       String url, Object request, Object response) {
        try {
            log.info("Log : {}", AppLog.getStringLog("INFO",
                    xcorrelation,
                    httpStatusCode,
                    action,
                    url,
                    objectMapper.writeValueAsString(request),
                    objectMapper.writeValueAsString(response)));
        } catch (JsonProcessingException ex) {
            log.error("JsonProcessingException: {}", ex.getMessage(), ex);
        }
    }
}
