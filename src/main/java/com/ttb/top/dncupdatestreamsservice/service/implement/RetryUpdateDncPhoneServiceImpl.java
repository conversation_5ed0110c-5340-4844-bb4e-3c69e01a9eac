package com.ttb.top.dncupdatestreamsservice.service.implement;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ttb.top.dncupdatestreamsservice.constants.Constants;
import com.ttb.top.dncupdatestreamsservice.exception.RetryUpdateDncPhoneException;
import com.ttb.top.dncupdatestreamsservice.feign.customerdata.CustomerDataServiceFeignClient;
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.customerprofile.response.CustomerFeignExceptionResponse;
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.getproductgroupid.response.ProductGroupIdResponse;
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.updatephonenumbers.request.UpdatePhoneNumbersRequest;
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.updatephonenumbers.response.UpdatePhoneNumbersResponse;
import com.ttb.top.dncupdatestreamsservice.model.mongo.dncupdatephonefail.DncPhoneUpdateFailTransaction;
import com.ttb.top.dncupdatestreamsservice.model.streams.producer.DncUpdateFailProducer;
import com.ttb.top.dncupdatestreamsservice.service.DncPhoneUpdateFailTransactionInsertService;
import com.ttb.top.dncupdatestreamsservice.service.RetryUpdateDncPhoneService;
import com.ttb.top.dncupdatestreamsservice.utils.AppLog;
import com.ttb.top.library.commonmodel.model.ResponseModel;
import feign.FeignException;
import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.retry.support.RetrySynchronizationManager;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@RequiredArgsConstructor
@Service
public class RetryUpdateDncPhoneServiceImpl implements RetryUpdateDncPhoneService {
    private final CustomerDataServiceFeignClient customerDataServiceFeignClient;
    private final DncPhoneUpdateFailTransactionInsertServiceImpl phoneUpdateFailTransactionInsertService;
    private final DncPhoneUpdateFailTransactionInsertService dncPhoneUpdateFailTransactionInsertService;
    private final ObjectMapper objectMapper;

    @Value("${feign.customer-data-service.custom-app.retry}")
    private final int maxAttemptsProperty;

    @Value("${feign.custom-app.dnclists-dncListId}")
    private final String dnclistsDncListId;

    private static final ObjectMapper mapper = new ObjectMapper();

    @Override
    public ResponseModel<UpdatePhoneNumbersResponse> updateDncPhoneWithRetry(
            HttpHeaders headers,
            String productGroupId,
            UpdatePhoneNumbersRequest updatePhoneNumbersRequest,
            String rmId,
            String ecId) throws RetryUpdateDncPhoneException {
        ResponseModel<UpdatePhoneNumbersResponse> response = new ResponseModel<>();
        // Get the current retry count from the RetrySynchronizationManager
        int currentRetryCount = RetrySynchronizationManager.getContext() != null
                ? RetrySynchronizationManager.getContext().getRetryCount() : 0;
        log.info("call POST /v1/customer-data-service/customers/dncslist/phonenumbers/{} attempt #{}",
                productGroupId, currentRetryCount + 1);

        try {
            // POST /v1/customer-data-service/customers/dncslist/phonenumbers/{productGroupId}
            response = customerDataServiceFeignClient.updatePhoneNumbers(headers,
                    productGroupId,
                    updatePhoneNumbersRequest);

            applicationLog(headers.getFirst(Objects.requireNonNull(headers.getFirst("x-correlation-id"))),
                    response.getStatus().getCode(),
                    response.getStatus().getCode().equalsIgnoreCase("0000"),
                    "POST /customers/dncslist/phonenumbers/" + productGroupId,
                    updatePhoneNumbersRequest,
                    response);
            return response;
        } catch (FeignException ex) {
            try {
                response = objectMapper.readValue(ex.contentUTF8(),
                        new TypeReference<ResponseModel<UpdatePhoneNumbersResponse>>() {});

            } catch (JsonProcessingException e) {
                log.error("JsonProcessingException: {}", e.getMessage(), e);
            }

            // Check if more retries are available and if the error code is 9004
            if ((currentRetryCount < maxAttemptsProperty - 1)) {
                String retryMessageException = "throw RetryUpdateDncPhoneException because FeignException, "
                        + " code: " + response.getCode()
                        + " status: " + response.getStatus().getCode()
                        + " description: " + response.getDescription()
                        + " header: " + response.getHeader();

                throw new RetryUpdateDncPhoneException(retryMessageException, updatePhoneNumbersRequest, response,
                        productGroupId, headers);
            }
        } catch (CallNotPermittedException ex) {
            String retryMessageException = "throw RetryUpdateDncPhoneException because CallNotPermittedException, "
                    + " code: " + response.getCode()
                    + " status: " + response.getStatus().getCode()
                    + " description: " + response.getDescription()
                    + " header: " + response.getHeader();

            throw new RetryUpdateDncPhoneException(retryMessageException, updatePhoneNumbersRequest, response,
                    productGroupId, headers);
        }

        return response;
    }

    @Override
    public DncUpdateFailProducer callUpdateDncPhoneWithRetry(
            HttpHeaders headers,
            String productGroupId,
            UpdatePhoneNumbersRequest updatePhoneNumbersRequest,
            String rmId,
            String ecId) {

        RetryUpdateDncPhoneService proxy = (RetryUpdateDncPhoneService) AopContext.currentProxy();
        ResponseModel<UpdatePhoneNumbersResponse> response = proxy.updateDncPhoneWithRetry(headers, productGroupId,
                updatePhoneNumbersRequest, rmId, ecId);

        List<DncUpdateFailProducer.DncUpdatePhone> dncUpdatePhonesFail = new ArrayList<>();
        List<DncUpdateFailProducer.DncUpdateError> errors = new ArrayList<>();

        if (response != null && response.getStatus() != null
                && "0000".equalsIgnoreCase(response.getStatus().getCode())) {
            // Success case
            log.info("DNC phone update successful for productGroupId: {}", productGroupId);
            return DncUpdateFailProducer.builder()
                    .body(Collections.emptyList())
                    .errors(Collections.emptyList())
                    .build();
        } else if (response != null && response.getStatus() != null) {

            // Failure case (either a direct failure not retried,
            // or a failure after retries where recoverFail returned ex.getLastResponse())
            log.warn("DNC phone update failed for productGroupId: {}. Constructing DncUpdateFailProducer.",
                    productGroupId);

            try {
                // Attempt to get product group ID details, this was previously in recoverFail
                // GET /v1/customer-data-service/customers/dncslist/{productGroupId}
                ResponseModel<ProductGroupIdResponse> productGroupIdResponse =
                        customerDataServiceFeignClient.getProductGroupId(headers, productGroupId);

                applicationLog(headers.getFirst(Objects.requireNonNull(headers.getFirst("x-correlation-id"))),
                        productGroupIdResponse.getStatus().getCode(),
                        productGroupIdResponse.getStatus().getCode().equalsIgnoreCase("0000"),
                        "GET /customers/dncslist/phonenumbers/" + productGroupId,
                        productGroupId,
                        productGroupIdResponse);

                if (productGroupIdResponse.getDataObj() != null) {
                    dncUpdatePhonesFail.add(DncUpdateFailProducer.DncUpdatePhone.builder()
                            .dncListId(productGroupIdResponse.getDataObj().getDncListId())
                            .dncList(DncUpdateFailProducer.DncList.builder()
                                    .action(updatePhoneNumbersRequest.getAction())
                                    .phoneNumbers(updatePhoneNumbersRequest.getPhoneNumbers())
                                    .expirationDateTime(updatePhoneNumbersRequest.getExpirationDateTime())
                                    .build())
                            .build());
                } else {
                    log.warn("Failed to retrieve ProductGroupIdResponse or data was null for productGroupId: {} "
                            + "while building DncUpdateFailProducer.", productGroupId);

                    //TEP Service processing error
                    DncPhoneUpdateFailTransaction.Body objectBody = DncPhoneUpdateFailTransaction.Body.builder()
                            .dncListId(productGroupId)
                            .dncList(DncPhoneUpdateFailTransaction.DncList.builder()
                                    .action(updatePhoneNumbersRequest.getAction())
                                    .phoneNumbers(updatePhoneNumbersRequest.getPhoneNumbers())
                                    .expirationDateTime(updatePhoneNumbersRequest.getExpirationDateTime())
                                    .build())
                            .build();

                    DncPhoneUpdateFailTransaction.Error errorObject = DncPhoneUpdateFailTransaction.Error.builder()
                            .serviceName(Constants.GET_DNCLIST_PHONENUMBER_ENDPOINT.concat(productGroupId))
                            .httpStatus(productGroupIdResponse.getStatus().getCode())
                            .code(productGroupIdResponse.getStatus().getCode())
                            .message(productGroupIdResponse.getHeader())
                            .status(productGroupIdResponse.getStatus().getDescription())
                            .build();

                    List<DncPhoneUpdateFailTransaction.Body> bodyList = new ArrayList<>();
                    bodyList.add(objectBody);
                    List<DncPhoneUpdateFailTransaction.Error> errorList = new ArrayList<>();
                    errorList.add(errorObject);

                    DncPhoneUpdateFailTransaction dncPhoneUpdateFailTransaction =
                            DncPhoneUpdateFailTransaction.builder()
                                    .dncBody(bodyList)
                                    .dncError(errorList)
                                    .build();

                    dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(
                            headers,
                            dncPhoneUpdateFailTransaction,
                            rmId,
                            ecId);
                }
            } catch (FeignException e) {
                log.error("Exception while fetching ProductGroupId for productGroupId: "
                        + "{} during failure processing.", productGroupId, e);

                try {
                    CustomerFeignExceptionResponse errorMap = objectMapper.readValue(e.contentUTF8(),
                            CustomerFeignExceptionResponse.class);

                    DncPhoneUpdateFailTransaction.Body objectBody = DncPhoneUpdateFailTransaction.Body.builder()
                            .dncListId(productGroupId)
                            .dncList(DncPhoneUpdateFailTransaction.DncList.builder()
                                    .action(updatePhoneNumbersRequest.getAction())
                                    .phoneNumbers(updatePhoneNumbersRequest.getPhoneNumbers())
                                    .expirationDateTime(updatePhoneNumbersRequest.getExpirationDateTime())
                                    .build())
                            .build();

                    DncPhoneUpdateFailTransaction.Error errorObject = DncPhoneUpdateFailTransaction.Error.builder()
                            .serviceName(Constants.GET_DNCLIST_PHONENUMBER_ENDPOINT.concat(productGroupId))
                            .httpStatus(String.valueOf(e.status()))
                            .code(errorMap.getStatus().getHeader())
                            .message(errorMap.getStatus().getDescription())
                            .status(errorMap.getStatus().getCode())
                            .build();

                    List<DncPhoneUpdateFailTransaction.Body> bodyList = new ArrayList<>();
                    bodyList.add(objectBody);
                    List<DncPhoneUpdateFailTransaction.Error> errorList = new ArrayList<>();
                    errorList.add(errorObject);

                    DncPhoneUpdateFailTransaction dncPhoneUpdateFailTransaction =
                            DncPhoneUpdateFailTransaction.builder()
                                    .dncBody(bodyList)
                                    .dncError(errorList)
                                    .build();

                    dncPhoneUpdateFailTransactionInsertService.insertTransactionDncPhoneUpdateFail(
                            headers,
                            dncPhoneUpdateFailTransaction,
                            rmId,
                            ecId);

                } catch (JsonProcessingException ex) {
                    log.error("JsonProcessingException: {}", ex.getMessage(), ex);
                }

                log.error("after catch FeignException insert mongo get phone number: {}", e.getMessage(), e);

                dncUpdatePhonesFail.add(DncUpdateFailProducer.DncUpdatePhone.builder()
                        //.dncListId(toProductGroupIdMap(productGroupId)) // get config
                        .dncListId(toProductGroupIdMap(productGroupId))
                        .dncList(DncUpdateFailProducer.DncList.builder()
                                .action(updatePhoneNumbersRequest.getAction())
                                .phoneNumbers(updatePhoneNumbersRequest.getPhoneNumbers())
                                .expirationDateTime(updatePhoneNumbersRequest.getExpirationDateTime())
                                .build())
                        .build());
            } catch (CallNotPermittedException ex) {

                var dncListId = toProductGroupIdMap(productGroupId);

                dncUpdatePhonesFail.add(DncUpdateFailProducer.DncUpdatePhone.builder()
                        //.dncListId(toProductGroupIdMap(productGroupId)) // get config
                        .dncListId(dncListId)
                        .dncList(DncUpdateFailProducer.DncList.builder()
                                .action(updatePhoneNumbersRequest.getAction())
                                .phoneNumbers(updatePhoneNumbersRequest.getPhoneNumbers())
                                .expirationDateTime(updatePhoneNumbersRequest.getExpirationDateTime())
                                .build())
                        .build());

                errors.add(DncUpdateFailProducer.DncUpdateError.builder()
                        .message("updateDncPhone failed,response from updatePhoneNumbers is null")
                        .code("9998")
                        .build());

            }

            if (response.getDataObj() != null && response.getDataObj().getCustomAppError() != null) {
                UpdatePhoneNumbersResponse errorData = response.getDataObj();
                errors.add(DncUpdateFailProducer.DncUpdateError.builder()
                        .message(errorData.getCustomAppError().getMessage())
                        .code(response.getStatus().getCode())
                        .httpStatus(errorData.getCustomAppError().getHttpStatus())
                        .build());
            } else if (response.getStatus() != null) {
                // Fallback if dataObj or CustomAppError is null, use status from ResponseModel
                errors.add(DncUpdateFailProducer.DncUpdateError.builder()
                        .message(response.getStatus().getDescription())
                        .code(response.getStatus().getCode())
                        .build());
            } else {
                log.warn("Response model, its data, or status was null when constructing DncUpdateFailProducer "
                        + "errors for productGroupId: {}.", productGroupId);

                errors.add(DncUpdateFailProducer.DncUpdateError.builder()
                        .message(response.getDataObj().getCustomAppError().getMessage())
                        .code(response.getDataObj().getCustomAppError().getCode())
                        .build());
            }
        } else {
            String responseCode = "9998";
            String responseDescription = "updateDncPhone failed,response from updatePhoneNumbers is null";
            log.warn("Unexpected response status code: {} for productGroupId: {} while constructing "
                            + "DncUpdateFailProducer.", responseCode, productGroupId);

            dncUpdatePhonesFail.add(DncUpdateFailProducer.DncUpdatePhone.builder()
                    .dncListId(toProductGroupIdMap(productGroupId)) // get config
                    .dncList(DncUpdateFailProducer.DncList.builder()
                            .action(updatePhoneNumbersRequest.getAction())
                            .phoneNumbers(updatePhoneNumbersRequest.getPhoneNumbers())
                            .expirationDateTime(updatePhoneNumbersRequest.getExpirationDateTime())
                            .build())
                    .build());

            errors.add(DncUpdateFailProducer.DncUpdateError.builder()
                    .message(responseDescription)
                    .code(responseCode)
                    .build());
        }


        return DncUpdateFailProducer.builder()
                .body(dncUpdatePhonesFail)
                .errors(errors)
                .build();
    }

    @Override
    public ResponseModel<UpdatePhoneNumbersResponse> recoverFail(RetryUpdateDncPhoneException ex,
                                                                 HttpHeaders headers,
                                                                 String productGroupId,
                                                                 UpdatePhoneNumbersRequest updatePhoneNumbersRequest,
                                                                 String rmId,
                                                                 String ecId) {

        log.error("Update DNC phone failed after all retry attempts for productGroupId: {}. Exception message: '{}'. "
                        + "Returning last known response.",
                ex.getProductGroupId(), ex.getMessage(), ex);

        return ex.getLastResponse();
    }

    private String toProductGroupIdMap(String propertyValue) {
        Map<String, String> result = new HashMap<>();

        String[] pairs = dnclistsDncListId.split("\\|");
        for (String pair : pairs) {
            String[] keyValue = pair.split(":", 2);
            if (keyValue.length == 2) {
                result.put(keyValue[0].trim(), keyValue[1].trim());
            }
        }
        return result.get(propertyValue.toLowerCase());
    }

    private static void applicationLog(String correlation, String httpStatusCode, boolean action,
                                       String url, Object request, Object response) {
        log.info("Log : {}", AppLog.getStringLog("INFO",
                correlation,
                httpStatusCode,
                action ? "Successfully" : "Error",
                url,
                request,
                response));
    }


}
