package com.ttb.top.dncupdatestreamsservice.service.implement;

import com.ttb.top.dncupdatestreamsservice.constants.Constants;
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.customerprofile.response.CustomerProfileResponse;
import com.ttb.top.dncupdatestreamsservice.service.CustomerCacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerCacheServiceImpl implements CustomerCacheService {
    @Override
    @Cacheable(
            value = Constants.CUSTOMER_CACHE_VALUE,
            key = "'customer_360:customer:crm:'.concat(#crmId)",
            unless = "#result == null",
            cacheManager = Constants.REDIS_CUSTOM_CACHE_MANAGER_NAME
    )
    public CustomerProfileResponse getCustomer(String crmId) {
        log.info("Fetching customer data for crmId: {}", crmId);
        return null;
    }

    @Override
    @CachePut(
            value = Constants.CUSTOMER_CACHE_VALUE,
            key = "'customer_360:customer:crm:'.concat(#crmId)",
            cacheManager = Constants.REDIS_CUSTOM_CACHE_MANAGER_NAME
    )
    public CustomerProfileResponse setCustomer(String crmId, CustomerProfileResponse data) {
        log.info("Caching customer data for rmId: {}", crmId);
        return data;
    }
}
