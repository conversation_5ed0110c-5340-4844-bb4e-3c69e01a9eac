package com.ttb.top.dncupdatestreamsservice.service;

import com.ttb.top.dncupdatestreamsservice.exception.RetryUpdateDncPhoneException;
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.updatephonenumbers.request.UpdatePhoneNumbersRequest;
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.updatephonenumbers.response.UpdatePhoneNumbersResponse;
import com.ttb.top.dncupdatestreamsservice.model.streams.producer.DncUpdateFailProducer;
import com.ttb.top.library.commonmodel.model.ResponseModel;
import org.springframework.http.HttpHeaders;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;

public interface RetryUpdateDncPhoneService {

    DncUpdateFailProducer callUpdateDncPhoneWithRetry(
            HttpHeaders headers,
            String productGroupId,
            UpdatePhoneNumbersRequest updatePhoneNumbersRequest,
            String rmId,
            String ecId);

    @Retryable(
            retryFor = { RetryUpdateDncPhoneException.class },
            maxAttemptsExpression = "${feign.customer-data-service.custom-app.retry}",
            backoff =  @Backoff(delayExpression = "${feign.customer-data-service.custom-app.delay.ms}"))
    ResponseModel<UpdatePhoneNumbersResponse> updateDncPhoneWithRetry(
            HttpHeaders headers,
            String productGroupId,
            UpdatePhoneNumbersRequest updatePhoneNumbersRequest,
            String rmId,
            String ecId) throws RetryUpdateDncPhoneException;

    @Recover
    ResponseModel<UpdatePhoneNumbersResponse> recoverFail(RetryUpdateDncPhoneException ex,
                                                                 HttpHeaders headers,
                                                                 String productGroupId,
                                                                 UpdatePhoneNumbersRequest updatePhoneNumbersRequest,
                                                                 String rmId,
                                                                 String ecId);
}


