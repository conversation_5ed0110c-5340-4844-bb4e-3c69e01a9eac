package com.ttb.top.dncupdatestreamsservice.repository.implement;

import com.ttb.top.dncupdatestreamsservice.repository.GenericRepository;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Repository;


@Repository
public abstract class GenericRepositoryImpl<T> implements GenericRepository<T> {

    protected final MongoTemplate mongoTemplate;

    protected GenericRepositoryImpl(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Override
    public void save(T document) {
        mongoTemplate.save(document);
    }
}
