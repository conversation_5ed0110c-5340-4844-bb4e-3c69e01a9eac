package com.ttb.top.dncupdatestreamsservice;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;

@EnableCaching
@EnableAsync(proxyTargetClass = true)
@SpringBootApplication(scanBasePackages = "com.ttb")
@EnableFeignClients(basePackages = "com.ttb")
@EnableRetry
@EnableAspectJAutoProxy(exposeProxy = true)
public class DncUpdateStreamServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(DncUpdateStreamServiceApplication.class, args);
    }

}