package com.ttb.top.dncupdatestreamsservice.exception;

import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.updatephonenumbers.request.UpdatePhoneNumbersRequest;
import com.ttb.top.dncupdatestreamsservice.model.feign.customerdata.updatephonenumbers.response.UpdatePhoneNumbersResponse;
import com.ttb.top.library.commonmodel.model.ResponseModel;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;

@Getter
@Slf4j
public class RetryUpdateDncPhoneException extends RuntimeException {
    private final UpdatePhoneNumbersRequest updatePhoneNumbersRequest;
    private final ResponseModel<UpdatePhoneNumbersResponse> lastResponse;
    private final String productGroupId;
    private final HttpHeaders headers;


    public RetryUpdateDncPhoneException(String message, UpdatePhoneNumbersRequest updatePhoneNumbersRequest,
                                        ResponseModel<UpdatePhoneNumbersResponse> lastResponse,
                                        String productGroupId, HttpHeaders headers) {
        super(message);
        this.lastResponse = lastResponse;
        this.updatePhoneNumbersRequest = updatePhoneNumbersRequest;
        this.productGroupId = productGroupId;
        this.headers = headers;

        log.info(message);
    }
}

