package com.ttb.top.dncupdatestreamsservice.streams.consumer;

import com.ttb.top.dncupdatestreamsservice.service.implement.DncUpdateConsumerServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.listener.adapter.ConsumerRecordMetadata;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class EventHubConsumer {

    private final DncUpdateConsumerServiceImpl dncUpdateConsumerService;

    @SneakyThrows
    @KafkaListener(topics = "${eventhub.consumer.dnc-phone.update.topic}",
            groupId = "${kafka.dnc-update-stream.group-id}",
            autoStartup = "${listen.auto.start}")
    public void listener(@Payload String message, ConsumerRecordMetadata meta) {
        log.info("Consume message from topic={}, partition={}, offset={}, timestamp={}, msg={}",
                meta.topic(), meta.partition(), meta.offset(), meta.timestamp(), message);

        dncUpdateConsumerService.processMessage(message);
    }
}
