package com.ttb.top.dncupdatestreamsservice.streams.producer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Component;

import java.util.concurrent.CancellationException;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

@Slf4j
@Component
@RequiredArgsConstructor
public class EventHubProducer<T> {
    private final KafkaTemplate<String, String> kafkaTemplate;
    private final ObjectMapper objectMapper;

    public Boolean send(String topicName, T data) {
        String message;
        try {
            log.info("send message {} to eventhub", data);
            message = objectMapper.writeValueAsString(data);

        } catch (JsonProcessingException e) {
            log.error("Failed to serialize message for topic='{}' due to: {}", topicName, e.getMessage(), e);
            return false;
        }

        try {
            // Wait for the future to complete and handle the result
            CompletableFuture<SendResult<String, String>> future = kafkaTemplate.send(topicName, message);

            // Flush to ensure the message is sent immediately
            kafkaTemplate.flush();

            SendResult<String, String> result = future.get();
            // send Successful
            if (result != null && result.getRecordMetadata() != null) {
                log.info("Sent message='{}' to topic='{}', partition={}, offset={}",
                        message, topicName, result.getRecordMetadata().partition(), result.getRecordMetadata()
                                .offset());

            } else {
                // Should ideally not happen on success, but good to log
                log.info("Sent message failed='{}' to topic='{}', because SendResult or RecordMetadata was null.",
                        message, topicName);
            }
            return true;

        } catch (ExecutionException ex) {
            Throwable cause = ex.getCause();
            log.info("Kafka/EventHub send failed for topic='{}': {}", topicName, cause.getMessage(), cause);
            return false;
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
            log.info("Kafka/EventHub send interrupted for topic='{}': {}", topicName, ex.getMessage(), ex);
            return false;
        } catch (CancellationException ex) {
            log.info("Kafka/EventHub send cancelled for topic='{}': {}", topicName, ex.getMessage(), ex);
            return false;
        } catch (Exception ex) {
            log.info("Kafka/EventHub failed to send message='{}' to topic='{}' due to: {}",
                    message, topicName, ex.getMessage(), ex);
            return false;
        }
    }
}
