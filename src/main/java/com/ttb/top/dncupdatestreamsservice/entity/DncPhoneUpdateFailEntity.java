package com.ttb.top.dncupdatestreamsservice.entity;

import com.ttb.top.dncupdatestreamsservice.constants.Constants;
import com.ttb.top.dncupdatestreamsservice.constants.FieldNameConstant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.List;

@Getter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
@Document(collection = Constants.CUSTOMER_360_DNC_PHONE_UPDATE_FAIL_TRANSACTION)

public class DncPhoneUpdateFailEntity {

    @Id
    @Field(FieldNameConstant.DNC_ID)
    private String id;
    @Field(FieldNameConstant.X_CORRELATION_ID)
    private String correlationId;
    @Field(FieldNameConstant.RM_ID)
    private String rmId;
    @Field(FieldNameConstant.EC_ID)
    private String ecId;
    @Field(FieldNameConstant.MODIFIED_ON)
    private String modifiedOn;
    @Field(FieldNameConstant.CREATED_ON)
    private String createdOn;
    @Field(FieldNameConstant.BODY)
    private List<Body> dncBody;
    @Field(FieldNameConstant.ERROR)
    private List<Error> dncError;

    @Getter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Body {

        @Field(FieldNameConstant.DNC_LIST_ID)
        private String dncListId;
        @Field(FieldNameConstant.DNC_LIST)
        private DncList dncList;

    }

    @Getter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DncList {

        @Field(FieldNameConstant.ACTION)
        private String action;
        @Field(FieldNameConstant.PHONE_NUMBERS)
        private List<String> phoneNumbers;
        @Field(FieldNameConstant.EXPIRATION_DATE_TIME)
        private String expirationDateTime;

    }

    @Getter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Error {

        @Field(FieldNameConstant.SERVICE_NAME)
        private String serviceName;
        @Field(FieldNameConstant.HTTP_STATUS)
        private String httpStatus;
        @Field(FieldNameConstant.CODE)
        private String code;
        @Field(FieldNameConstant.MESSAGE)
        private String message;
        @Field(FieldNameConstant.STATUS)
        private String status;

    }
}


