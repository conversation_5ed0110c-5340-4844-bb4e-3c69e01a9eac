package com.ttb.top.dncupdatestreamsservice.constants;

import lombok.experimental.UtilityClass;

@UtilityClass
public class Constants {

    /* Cache Value */
    public static final String REDIS_CUSTOM_CACHE_MANAGER_NAME = "customRedisCacheManager";
    public static final String CUSTOMER_CACHE_VALUE = "customer_360-get-customer";
    public static final String CUSTOMER_360_DNC_PHONE_UPDATE_FAIL_TRANSACTION =
            "customer_360_dnc_update_fail";

    /* Service Name */
    public static final String GET_CUSTOMER_PROFILE_ENDPOINT =
            "/v1/customer-data-service/customer-profile/get-customer";
    public static final String POST_DNCLIST_PHONENUMBER_ENDPOINT =
            "/v1/customer-data-service/customers/dncslist/phonenumbers/";
    public static final String GET_DNCLIST_PHONENUMBER_ENDPOINT =
            "/v1/customer-data-service/customers/dncslist/";
}
