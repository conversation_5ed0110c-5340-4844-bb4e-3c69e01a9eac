#Dockerfile (create file in the project directory)
FROM nexus.tmbbank.local:60021/openjdk-17:1.23-3.**********.aks
USER 0
# Java security to backward suppport Kafka
RUN update-crypto-policies --set LEGACY
# Set Asia/Bangkok timezone
RUN ln -snf "/usr/share/zoneinfo/Asia/Bangkok" /etc/localtime && echo "Asia/Bangkok" > /etc/timezone

WORKDIR /app
COPY *.jar /app/app.jar
RUN mkdir /logs && chown -R jboss:jboss /logs
RUN chmod -R 777 /app && chown -R jboss:jboss /app

#185 = jboss user
USER 185
EXPOSE 8000 8081
ENTRYPOINT ["java","-Dfile.encoding=UTF-8", "-Duser.timezone=GMT+7", "-Djava.net.preferIPv4Stack=true", "-Djava.security.egd=file:/dev/./urandom", "-jar", "/app/app.jar"]
